#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class Deployer {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.buildDir = path.join(this.projectRoot, 'dist');
    this.config = this.loadConfig();
  }

  loadConfig() {
    const configPath = path.join(this.projectRoot, 'deploy.config.json');
    
    if (fs.existsSync(configPath)) {
      return JSON.parse(fs.readFileSync(configPath, 'utf8'));
    }
    
    // 默认配置
    return {
      environments: {
        development: {
          name: 'Development',
          url: 'http://localhost:3000',
          buildCommand: 'npm run dev'
        },
        staging: {
          name: 'Staging',
          url: 'https://staging.example.com',
          buildCommand: 'npm run build',
          deployCommand: 'docker-compose -f docker-compose.staging.yml up -d'
        },
        production: {
          name: 'Production',
          url: 'https://production.example.com',
          buildCommand: 'npm run build',
          deployCommand: 'docker-compose -f docker-compose.prod.yml up -d',
          preDeployChecks: true
        }
      },
      docker: {
        registry: 'your-registry.com',
        imageName: 'liu-image-gallery',
        tag: 'latest'
      }
    };
  }

  async deploy(environment = 'development') {
    console.log(`🚀 开始部署到 ${environment} 环境...`);
    
    const envConfig = this.config.environments[environment];
    if (!envConfig) {
      throw new Error(`未找到环境配置: ${environment}`);
    }

    try {
      // 预部署检查
      if (envConfig.preDeployChecks) {
        await this.runPreDeployChecks();
      }

      // 构建项目
      await this.buildProject(envConfig);

      // 运行测试
      if (environment !== 'development') {
        await this.runTests();
      }

      // 部署
      await this.deployToEnvironment(envConfig);

      // 后部署验证
      await this.postDeployVerification(envConfig);

      console.log(`✅ 部署到 ${environment} 环境成功!`);
      console.log(`🌐 访问地址: ${envConfig.url}`);

    } catch (error) {
      console.error(`❌ 部署失败:`, error.message);
      process.exit(1);
    }
  }

  async runPreDeployChecks() {
    console.log('🔍 运行预部署检查...');

    // 检查Git状态
    try {
      const gitStatus = execSync('git status --porcelain', { encoding: 'utf8' });
      if (gitStatus.trim()) {
        console.warn('⚠️  工作目录有未提交的更改');
      }
    } catch (error) {
      console.warn('⚠️  无法检查Git状态');
    }

    // 检查依赖
    if (!fs.existsSync(path.join(this.projectRoot, 'node_modules'))) {
      console.log('📦 安装依赖...');
      execSync('npm ci', { cwd: this.projectRoot, stdio: 'inherit' });
    }

    // 检查环境变量
    this.checkEnvironmentVariables();

    console.log('✅ 预部署检查完成');
  }

  checkEnvironmentVariables() {
    const requiredEnvVars = [
      'NODE_ENV'
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
      console.warn(`⚠️  缺少环境变量: ${missingVars.join(', ')}`);
    }
  }

  async buildProject(envConfig) {
    console.log('🔨 构建项目...');

    try {
      execSync(envConfig.buildCommand, { 
        cwd: this.projectRoot, 
        stdio: 'inherit' 
      });
      console.log('✅ 项目构建完成');
    } catch (error) {
      throw new Error(`构建失败: ${error.message}`);
    }
  }

  async runTests() {
    console.log('🧪 运行测试...');

    try {
      // 运行单元测试
      if (fs.existsSync(path.join(this.projectRoot, 'tests'))) {
        console.log('运行集成测试...');
        // 这里可以添加实际的测试运行逻辑
      }

      console.log('✅ 测试通过');
    } catch (error) {
      throw new Error(`测试失败: ${error.message}`);
    }
  }

  async deployToEnvironment(envConfig) {
    console.log(`📦 部署到 ${envConfig.name}...`);

    if (envConfig.deployCommand) {
      try {
        execSync(envConfig.deployCommand, { 
          cwd: this.projectRoot, 
          stdio: 'inherit' 
        });
      } catch (error) {
        throw new Error(`部署命令执行失败: ${error.message}`);
      }
    } else {
      // 默认部署逻辑
      await this.defaultDeploy(envConfig);
    }
  }

  async defaultDeploy(envConfig) {
    // 复制文件到部署目录
    const deployDir = path.join(this.projectRoot, 'deploy');
    
    if (!fs.existsSync(deployDir)) {
      fs.mkdirSync(deployDir, { recursive: true });
    }

    // 复制必要文件
    const filesToCopy = [
      'index.html',
      'src',
      'public',
      'package.json',
      'vite.config.js',
      'tailwind.config.js'
    ];

    filesToCopy.forEach(file => {
      const srcPath = path.join(this.projectRoot, file);
      const destPath = path.join(deployDir, file);
      
      if (fs.existsSync(srcPath)) {
        this.copyRecursive(srcPath, destPath);
      }
    });

    console.log('✅ 文件复制完成');
  }

  copyRecursive(src, dest) {
    const stat = fs.statSync(src);
    
    if (stat.isDirectory()) {
      if (!fs.existsSync(dest)) {
        fs.mkdirSync(dest, { recursive: true });
      }
      
      fs.readdirSync(src).forEach(file => {
        this.copyRecursive(
          path.join(src, file),
          path.join(dest, file)
        );
      });
    } else {
      fs.copyFileSync(src, dest);
    }
  }

  async postDeployVerification(envConfig) {
    console.log('🔍 部署后验证...');

    // 等待服务启动
    await this.delay(5000);

    // 健康检查
    try {
      const healthCheck = await this.performHealthCheck(envConfig.url);
      if (healthCheck) {
        console.log('✅ 健康检查通过');
      } else {
        console.warn('⚠️  健康检查失败');
      }
    } catch (error) {
      console.warn('⚠️  无法执行健康检查:', error.message);
    }
  }

  async performHealthCheck(url) {
    // 简单的健康检查
    try {
      const response = await fetch(url);
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  async buildDockerImage() {
    console.log('🐳 构建Docker镜像...');

    const { registry, imageName, tag } = this.config.docker;
    const fullImageName = `${registry}/${imageName}:${tag}`;

    try {
      execSync(`docker build -t ${fullImageName} .`, {
        cwd: this.projectRoot,
        stdio: 'inherit'
      });

      console.log(`✅ Docker镜像构建完成: ${fullImageName}`);
      return fullImageName;
    } catch (error) {
      throw new Error(`Docker镜像构建失败: ${error.message}`);
    }
  }

  async pushDockerImage(imageName) {
    console.log('📤 推送Docker镜像...');

    try {
      execSync(`docker push ${imageName}`, {
        cwd: this.projectRoot,
        stdio: 'inherit'
      });

      console.log('✅ Docker镜像推送完成');
    } catch (error) {
      throw new Error(`Docker镜像推送失败: ${error.message}`);
    }
  }

  generateDeploymentReport(environment, startTime) {
    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);

    const report = {
      environment,
      startTime: new Date(startTime).toISOString(),
      endTime: new Date(endTime).toISOString(),
      duration: `${duration}秒`,
      status: 'success',
      version: this.getVersion(),
      commit: this.getCommitHash()
    };

    const reportPath = path.join(this.projectRoot, 'deployment-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    console.log(`📊 部署报告已生成: ${reportPath}`);
    return report;
  }

  getVersion() {
    try {
      const packageJson = JSON.parse(
        fs.readFileSync(path.join(this.projectRoot, 'package.json'), 'utf8')
      );
      return packageJson.version || '1.0.0';
    } catch (error) {
      return '1.0.0';
    }
  }

  getCommitHash() {
    try {
      return execSync('git rev-parse HEAD', { encoding: 'utf8' }).trim();
    } catch (error) {
      return 'unknown';
    }
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// CLI接口
if (require.main === module) {
  const environment = process.argv[2] || 'development';
  const deployer = new Deployer();
  
  deployer.deploy(environment).catch(error => {
    console.error('部署失败:', error);
    process.exit(1);
  });
}

module.exports = Deployer;
