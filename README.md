# Liu 图库项目

基于现代前端技术栈的智能图片采集与展示平台。

## 技术栈

- **前端框架**: HTML5 + Vite
- **样式**: Tailwind CSS (JIT模式) + 组件化设计
- **动画**: Anime.js + RequestAnimationFrame
- **并发处理**: Web Workers
- **容器化**: Docker + Docker Compose

## 功能特性

- 🖼️ 智能图片数据采集
- 🎨 响应式图片展示界面
- ⚡ 高性能动画效果
- 🔄 Web Workers并发处理
- 📱 移动端适配
- 🐳 Docker容器化部署

## 快速开始

### 本地开发

1. 克隆项目
```bash
git clone <repository-url>
cd liu-image-gallery
```

2. 安装依赖
```bash
npm install
```

3. 启动开发服务器
```bash
npm run dev
```

4. 打开浏览器访问 `http://localhost:3000`

### Docker部署

1. 构建并启动服务
```bash
docker-compose up -d
```

2. 访问应用
- 前端应用: `http://localhost:3000`
- 数据库: `localhost:5432`
- Redis: `localhost:6379`

## 项目结构

```
liu-image-gallery/
├── src/
│   ├── components/          # 组件
│   │   └── ImageGallery.js
│   ├── services/           # 服务
│   │   └── DataCollector.js
│   ├── utils/              # 工具类
│   │   └── AnimationController.js
│   ├── workers/            # Web Workers
│   │   └── WorkerManager.js
│   └── main.js             # 入口文件
├── public/                 # 静态资源
├── docker-compose.yml      # Docker编排
├── Dockerfile             # Docker镜像
├── vite.config.js         # Vite配置
├── tailwind.config.js     # Tailwind配置
└── package.json           # 项目配置
```

## 核心模块

### 1. 图片展示 (ImageGallery)
- 多种视图模式：网格、列表、瀑布流
- 响应式布局
- 图片懒加载
- 交互动画

### 2. 数据采集 (DataCollector)
- 多数据源支持
- 并发采集
- 实时统计
- 错误处理

### 3. 动画控制 (AnimationController)
- 预设动画效果
- 序列和并行动画
- 滚动触发动画
- 性能优化

### 4. Worker管理 (WorkerManager)
- 图片处理Worker
- 数据处理Worker
- 任务队列管理
- 性能监控

## 开发指南

### 添加新的动画效果

```javascript
// 在AnimationController中添加预设
this.presets.newAnimation = {
  scale: [0, 1],
  rotate: [0, 360],
  duration: 1000,
  ease: 'easeOutBack'
};

// 使用动画
animationController.animate('.element', 'newAnimation');
```

### 扩展数据采集源

```javascript
// 在DataCollector中添加新的数据源
this.dataSources.push({
  name: 'NewSource',
  url: 'https://api.newsource.com',
  apiKey: 'your-api-key',
  enabled: true
});

// 实现采集方法
async collectFromNewSource(source) {
  // 实现采集逻辑
}
```

### 创建新的Worker

```javascript
// 在WorkerManager中创建新Worker
this.createWorker('newWorker', '/src/workers/newWorker.js');

// 提交任务
const result = await workerManager.submitTask('newWorker', 'taskType', data);
```

## 性能优化

- 使用Web Workers处理CPU密集型任务
- 图片懒加载和虚拟滚动
- 动画使用RAF优化
- 组件级别的状态管理
- 资源预加载和缓存

## 浏览器支持

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 部署说明

### 环境变量

```bash
NODE_ENV=production
REDIS_URL=redis://localhost:6379
DATABASE_URL=postgresql://user:password@localhost:5432/database
```

### 生产构建

```bash
npm run build
npm run serve
```

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

MIT License

## 测试

### 运行测试

```bash
# 集成测试
npm run test:integration

# 性能测试
npm run test:performance

# 在浏览器中运行测试
# 访问 http://localhost:3000?quick=true 运行快速测试
# 访问 http://localhost:3000?test=true 运行集成测试
# 访问 http://localhost:3000?perf=true 运行性能测试
```

### 测试覆盖

- ✅ 应用初始化测试
- ✅ 数据采集功能测试
- ✅ 图片库显示测试
- ✅ 搜索功能测试
- ✅ Worker管理器测试
- ✅ 动画系统测试
- ✅ 设置管理测试
- ✅ 性能基准测试

## 部署

### 本地部署

```bash
# 开发环境
npm run deploy:dev

# 构建生产版本
npm run build
npm run serve
```

### Docker部署

```bash
# 构建Docker镜像
npm run docker:build

# 运行Docker容器
npm run docker:run

# 使用Docker Compose
docker-compose up -d
```

### 生产部署

```bash
# 部署到staging环境
npm run deploy:staging

# 部署到生产环境
npm run deploy:prod
```

## 高级功能

### 粒子系统

```javascript
// 创建粒子系统
const particles = animationController.createParticleSystem(container, {
  particleCount: 50,
  particleColor: '#3b82f6',
  interactive: true
});

// 启动粒子效果
particles.start();

// 创建爆发效果
particles.burst(x, y, 15);
```

### 手势识别

```javascript
// 创建手势识别器
const gestures = animationController.createGestureRecognizer(element);

// 绑定手势事件
gestures
  .onSwipeLeft(() => console.log('向左滑动'))
  .onPinchOut(() => console.log('放大手势'))
  .onDoubleTap(() => console.log('双击'));
```

### 滚动动画

```javascript
// 创建滚动触发动画
animationController.createScrollAnimation('.image-item', {
  animation: 'fadeInUp',
  stagger: 100,
  threshold: 0.2
});

// 创建视差效果
animationController.createParallax('.background', {
  speed: 0.5
});
```

### Worker处理

```javascript
// 图片处理
const result = await workerManager.processImage(imageUrl, {
  generateThumbnails: true,
  extractColors: true,
  analyzeQuality: true
});

// 数据分析
const analysis = await workerManager.analyzeData(images);
```

## 性能优化

- 🚀 Web Workers并行处理
- 🖼️ 图片懒加载和虚拟滚动
- ⚡ RequestAnimationFrame优化动画
- 💾 智能缓存策略
- 📱 响应式设计优化
- 🔄 组件级状态管理

## 故障排除

### 常见问题

1. **Worker无法加载**
   - 确保Worker文件路径正确
   - 检查浏览器是否支持Web Workers

2. **动画性能问题**
   - 启用"减少动画"选项
   - 检查硬件加速是否开启

3. **API请求失败**
   - 检查API密钥配置
   - 验证网络连接

4. **内存使用过高**
   - 减少批处理大小
   - 启用图片压缩

### 调试模式

```bash
# 启用详细日志
DEBUG=true npm run dev

# 启用性能监控
PERF_MONITOR=true npm run dev
```

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

### 开发规范

- 使用ES6+语法
- 遵循JSDoc注释规范
- 保持代码简洁和可读性
- 添加适当的错误处理
- 编写测试用例

## 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 更新日志

### v1.0.0 (2024-01-XX)
- ✨ 初始版本发布
- 🎨 完整的图片库系统
- ⚡ Web Workers并行处理
- 🎭 高级动画系统
- 🔍 智能搜索功能
- ⚙️ 完整的设置管理
- 📊 性能监控面板
- 🧪 集成测试套件

## 联系方式

- 作者: Liu
- 邮箱: [<EMAIL>]
- 项目链接: [https://github.com/your-username/liu-image-gallery]

## 致谢

感谢以下开源项目的支持：
- [Vite](https://vitejs.dev/) - 快速的构建工具
- [Tailwind CSS](https://tailwindcss.com/) - 实用优先的CSS框架
- [Anime.js](https://animejs.com/) - 轻量级动画库
