<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Liu 图库 - 简单测试</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .test-container {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      margin-bottom: 20px;
    }
    .test-result {
      padding: 10px;
      margin: 5px 0;
      border-radius: 4px;
      border-left: 4px solid;
    }
    .test-pass {
      background-color: #d4edda;
      border-color: #28a745;
      color: #155724;
    }
    .test-fail {
      background-color: #f8d7da;
      border-color: #dc3545;
      color: #721c24;
    }
    .test-info {
      background-color: #d1ecf1;
      border-color: #17a2b8;
      color: #0c5460;
    }
    button {
      background-color: #007bff;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px;
    }
    button:hover {
      background-color: #0056b3;
    }
    .iframe-container {
      border: 2px solid #ddd;
      border-radius: 8px;
      overflow: hidden;
      margin-top: 20px;
    }
    iframe {
      width: 100%;
      height: 600px;
      border: none;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h1>🧪 Liu 图库 - 简单测试页面</h1>
    <p>这个页面可以帮助你快速测试 Liu 图库应用的基本功能。</p>
    
    <div id="test-results">
      <div class="test-info">
        <strong>测试说明:</strong>
        <ul>
          <li>点击下面的按钮来测试不同功能</li>
          <li>绿色表示测试通过，红色表示测试失败</li>
          <li>可以在下方的iframe中查看实际应用</li>
        </ul>
      </div>
    </div>
    
    <div style="margin: 20px 0;">
      <button onclick="testBasicLoad()">测试基本加载</button>
      <button onclick="testQuickTest()">运行快速测试</button>
      <button onclick="testIntegration()">运行集成测试</button>
      <button onclick="testPerformance()">运行性能测试</button>
      <button onclick="clearResults()">清除结果</button>
    </div>
  </div>
  
  <div class="iframe-container">
    <iframe id="app-frame" src="http://localhost:3000"></iframe>
  </div>

  <script>
    function addTestResult(name, passed, message) {
      const resultsDiv = document.getElementById('test-results');
      const resultDiv = document.createElement('div');
      resultDiv.className = `test-result ${passed ? 'test-pass' : 'test-fail'}`;
      resultDiv.innerHTML = `
        <strong>${passed ? '✅' : '❌'} ${name}:</strong> ${message}
        <small style="float: right;">${new Date().toLocaleTimeString()}</small>
      `;
      resultsDiv.appendChild(resultDiv);
    }

    function addInfoResult(message) {
      const resultsDiv = document.getElementById('test-results');
      const resultDiv = document.createElement('div');
      resultDiv.className = 'test-result test-info';
      resultDiv.innerHTML = `
        <strong>ℹ️ 信息:</strong> ${message}
        <small style="float: right;">${new Date().toLocaleTimeString()}</small>
      `;
      resultsDiv.appendChild(resultDiv);
    }

    function clearResults() {
      const resultsDiv = document.getElementById('test-results');
      const infoDiv = resultsDiv.querySelector('.test-info');
      resultsDiv.innerHTML = '';
      if (infoDiv) {
        resultsDiv.appendChild(infoDiv);
      }
    }

    async function testBasicLoad() {
      addInfoResult('开始测试基本加载...');
      
      try {
        // 测试服务器连接
        const response = await fetch('http://localhost:3000');
        if (response.ok) {
          addTestResult('服务器连接', true, '开发服务器正常运行');
        } else {
          addTestResult('服务器连接', false, `服务器响应错误: ${response.status}`);
          return;
        }
        
        // 测试静态资源
        const cssResponse = await fetch('http://localhost:3000/src/main.js');
        if (cssResponse.ok) {
          addTestResult('静态资源', true, '主要JavaScript文件可访问');
        } else {
          addTestResult('静态资源', false, '主要JavaScript文件无法访问');
        }
        
        // 更新iframe
        const iframe = document.getElementById('app-frame');
        iframe.src = 'http://localhost:3000';
        
        addTestResult('基本加载测试', true, '所有基本检查都通过');
        
      } catch (error) {
        addTestResult('基本加载测试', false, `网络错误: ${error.message}`);
      }
    }

    function testQuickTest() {
      addInfoResult('启动快速测试...');
      const iframe = document.getElementById('app-frame');
      iframe.src = 'http://localhost:3000?quick=true';
      
      // 等待测试完成
      setTimeout(() => {
        addTestResult('快速测试', true, '快速测试已启动，请查看iframe中的结果');
      }, 2000);
    }

    function testIntegration() {
      addInfoResult('启动集成测试...');
      const iframe = document.getElementById('app-frame');
      iframe.src = 'http://localhost:3000?test=true';
      
      setTimeout(() => {
        addTestResult('集成测试', true, '集成测试已启动，请查看iframe中的结果');
      }, 2000);
    }

    function testPerformance() {
      addInfoResult('启动性能测试...');
      const iframe = document.getElementById('app-frame');
      iframe.src = 'http://localhost:3000?perf=true';
      
      setTimeout(() => {
        addTestResult('性能测试', true, '性能测试已启动，请查看iframe中的结果');
      }, 2000);
    }

    // 监听iframe加载事件
    document.getElementById('app-frame').addEventListener('load', function() {
      addInfoResult('应用页面已加载');
    });

    // 页面加载时自动运行基本测试
    window.addEventListener('load', function() {
      setTimeout(testBasicLoad, 1000);
    });
  </script>
</body>
</html>
