{"name": "animejs", "version": "3.2.2", "homepage": "http://animejs.com", "repository": "juliangarnier/anime", "description": "JavaScript animation engine", "umd:main": "lib/anime.min.js", "module": "lib/anime.es.js", "main": "lib/anime.js", "umd:name": "anime", "license": "MIT", "author": "<PERSON> <<EMAIL>>", "files": ["lib"], "scripts": {"build": "node build"}, "bugs": {"url": "https://github.com/juliangarnier/anime/issues"}, "keywords": ["anime", "animation", "javascript", "CSS", "transforms", "SVG", "canvas"], "devDependencies": {"gzip-size": "^4.1.0", "pretty-bytes": "^4.0.2", "rollup": "^0.53.2", "rollup-plugin-buble": "^0.18.0", "uglify-js": "^3.3.4"}}