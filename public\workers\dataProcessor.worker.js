// 数据处理Worker
self.onmessage = function(event) {
  const { taskId, type, data } = event.data;
  
  try {
    switch (type) {
      case 'processApiData':
        processApiData(taskId, data);
        break;
      case 'filterData':
        filterData(taskId, data);
        break;
      case 'sortData':
        sortData(taskId, data);
        break;
      case 'analyzeData':
        analyzeData(taskId, data);
        break;
      case 'searchData':
        searchData(taskId, data);
        break;
      case 'aggregateData':
        aggregateData(taskId, data);
        break;
      case 'validateData':
        validateData(taskId, data);
        break;
      case 'transformData':
        transformData(taskId, data);
        break;
      default:
        throw new Error('未知的数据处理任务类型: ' + type);
    }
  } catch (error) {
    self.postMessage({
      taskId,
      type: 'error',
      error: error.message
    });
  }
};

function processApiData(taskId, data) {
  const { rawData, source, options } = data;
  
  // 模拟API数据处理
  setTimeout(() => {
    const processedData = rawData.map((item, index) => ({
      ...item,
      processed: true,
      source: source,
      processedAt: new Date().toISOString(),
      index: index,
      hash: generateHash(JSON.stringify(item)),
      score: Math.random() * 100,
      tags: generateTags(item),
      category: categorizeItem(item)
    }));
    
    self.postMessage({
      taskId,
      type: 'processApiData',
      result: {
        data: processedData,
        count: processedData.length,
        source: source,
        processingTime: Date.now(),
        options: options
      }
    });
  }, 300 + Math.random() * 500);
}

function filterData(taskId, data) {
  const { items, filters } = data;
  
  let filteredItems = [...items];
  
  // 应用各种过滤器
  if (filters.category) {
    filteredItems = filteredItems.filter(item => 
      item.category === filters.category
    );
  }
  
  if (filters.tags && filters.tags.length > 0) {
    filteredItems = filteredItems.filter(item =>
      filters.tags.some(tag => 
        item.tags && item.tags.includes(tag)
      )
    );
  }
  
  if (filters.dateRange) {
    const { start, end } = filters.dateRange;
    filteredItems = filteredItems.filter(item => {
      const itemDate = new Date(item.createdAt || item.date);
      return itemDate >= new Date(start) && itemDate <= new Date(end);
    });
  }
  
  if (filters.sizeRange) {
    const { min, max } = filters.sizeRange;
    filteredItems = filteredItems.filter(item => {
      const size = item.width * item.height;
      return size >= min && size <= max;
    });
  }
  
  if (filters.source) {
    filteredItems = filteredItems.filter(item => 
      item.source === filters.source
    );
  }
  
  if (filters.quality) {
    filteredItems = filteredItems.filter(item => 
      item.quality && item.quality.score >= filters.quality
    );
  }
  
  if (filters.search) {
    const searchTerm = filters.search.toLowerCase();
    filteredItems = filteredItems.filter(item =>
      (item.title && item.title.toLowerCase().includes(searchTerm)) ||
      (item.description && item.description.toLowerCase().includes(searchTerm)) ||
      (item.tags && item.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
    );
  }
  
  self.postMessage({
    taskId,
    type: 'filterData',
    result: {
      items: filteredItems,
      originalCount: items.length,
      filteredCount: filteredItems.length,
      filters: filters,
      filteredAt: Date.now()
    }
  });
}

function sortData(taskId, data) {
  const { items, sortBy, order } = data;
  
  const sortedItems = [...items].sort((a, b) => {
    let aVal = getNestedValue(a, sortBy);
    let bVal = getNestedValue(b, sortBy);
    
    // 处理不同数据类型
    if (sortBy.includes('date') || sortBy.includes('time')) {
      aVal = new Date(aVal);
      bVal = new Date(bVal);
    } else if (typeof aVal === 'string' && typeof bVal === 'string') {
      aVal = aVal.toLowerCase();
      bVal = bVal.toLowerCase();
    }
    
    if (order === 'desc') {
      return bVal > aVal ? 1 : bVal < aVal ? -1 : 0;
    } else {
      return aVal > bVal ? 1 : aVal < bVal ? -1 : 0;
    }
  });
  
  self.postMessage({
    taskId,
    type: 'sortData',
    result: {
      items: sortedItems,
      sortBy: sortBy,
      order: order,
      sortedAt: Date.now()
    }
  });
}

function analyzeData(taskId, data) {
  const { items } = data;
  
  // 数据分析
  const analysis = {
    totalItems: items.length,
    categories: {},
    tags: {},
    sources: {},
    dateRange: {
      earliest: null,
      latest: null
    },
    sizeStats: {
      totalPixels: 0,
      averageWidth: 0,
      averageHeight: 0,
      minSize: Infinity,
      maxSize: 0
    },
    qualityStats: {
      averageScore: 0,
      highQuality: 0,
      mediumQuality: 0,
      lowQuality: 0
    }
  };
  
  let totalSize = 0;
  let totalWidth = 0;
  let totalHeight = 0;
  let totalQualityScore = 0;
  let qualityCount = 0;
  
  items.forEach(item => {
    // 分类统计
    if (item.category) {
      analysis.categories[item.category] = 
        (analysis.categories[item.category] || 0) + 1;
    }
    
    // 标签统计
    if (item.tags) {
      item.tags.forEach(tag => {
        analysis.tags[tag] = (analysis.tags[tag] || 0) + 1;
      });
    }
    
    // 来源统计
    if (item.source) {
      analysis.sources[item.source] = 
        (analysis.sources[item.source] || 0) + 1;
    }
    
    // 尺寸统计
    if (item.width && item.height) {
      const size = item.width * item.height;
      totalSize += size;
      totalWidth += item.width;
      totalHeight += item.height;
      analysis.sizeStats.totalPixels += size;
      analysis.sizeStats.minSize = Math.min(analysis.sizeStats.minSize, size);
      analysis.sizeStats.maxSize = Math.max(analysis.sizeStats.maxSize, size);
    }
    
    // 质量统计
    if (item.quality && item.quality.score) {
      totalQualityScore += item.quality.score;
      qualityCount++;
      
      if (item.quality.score >= 80) {
        analysis.qualityStats.highQuality++;
      } else if (item.quality.score >= 60) {
        analysis.qualityStats.mediumQuality++;
      } else {
        analysis.qualityStats.lowQuality++;
      }
    }
    
    // 日期范围
    const itemDate = new Date(item.createdAt || item.date);
    if (!isNaN(itemDate.getTime())) {
      if (!analysis.dateRange.earliest || itemDate < analysis.dateRange.earliest) {
        analysis.dateRange.earliest = itemDate;
      }
      if (!analysis.dateRange.latest || itemDate > analysis.dateRange.latest) {
        analysis.dateRange.latest = itemDate;
      }
    }
  });
  
  // 计算平均值
  if (items.length > 0) {
    analysis.sizeStats.averageWidth = Math.round(totalWidth / items.length);
    analysis.sizeStats.averageHeight = Math.round(totalHeight / items.length);
  }
  
  if (qualityCount > 0) {
    analysis.qualityStats.averageScore = Math.round(totalQualityScore / qualityCount);
  }
  
  // 修复无限值
  if (analysis.sizeStats.minSize === Infinity) {
    analysis.sizeStats.minSize = 0;
  }
  
  self.postMessage({
    taskId,
    type: 'analyzeData',
    result: analysis
  });
}

function searchData(taskId, data) {
  const { items, query, options } = data;
  
  const searchOptions = {
    caseSensitive: false,
    fuzzy: false,
    fields: ['title', 'description', 'tags'],
    ...options
  };
  
  const searchTerm = searchOptions.caseSensitive ? query : query.toLowerCase();
  
  const results = items.filter(item => {
    return searchOptions.fields.some(field => {
      const value = getNestedValue(item, field);
      if (!value) return false;
      
      if (Array.isArray(value)) {
        return value.some(v => 
          searchOptions.caseSensitive ? 
            v.includes(searchTerm) : 
            v.toLowerCase().includes(searchTerm)
        );
      } else {
        const stringValue = searchOptions.caseSensitive ? 
          String(value) : 
          String(value).toLowerCase();
        return stringValue.includes(searchTerm);
      }
    });
  });
  
  self.postMessage({
    taskId,
    type: 'searchData',
    result: {
      items: results,
      query: query,
      totalResults: results.length,
      searchedAt: Date.now(),
      options: searchOptions
    }
  });
}

function aggregateData(taskId, data) {
  const { items, groupBy, aggregations } = data;
  
  const groups = {};
  
  // 分组
  items.forEach(item => {
    const groupKey = getNestedValue(item, groupBy) || 'unknown';
    if (!groups[groupKey]) {
      groups[groupKey] = [];
    }
    groups[groupKey].push(item);
  });
  
  // 聚合
  const result = {};
  Object.keys(groups).forEach(key => {
    const group = groups[key];
    result[key] = {
      count: group.length,
      items: group
    };
    
    // 应用聚合函数
    if (aggregations) {
      aggregations.forEach(agg => {
        const values = group.map(item => getNestedValue(item, agg.field)).filter(v => v != null);
        
        switch (agg.function) {
          case 'sum':
            result[key][agg.name || `${agg.field}_sum`] = values.reduce((a, b) => a + b, 0);
            break;
          case 'avg':
            result[key][agg.name || `${agg.field}_avg`] = values.length > 0 ? values.reduce((a, b) => a + b, 0) / values.length : 0;
            break;
          case 'min':
            result[key][agg.name || `${agg.field}_min`] = values.length > 0 ? Math.min(...values) : null;
            break;
          case 'max':
            result[key][agg.name || `${agg.field}_max`] = values.length > 0 ? Math.max(...values) : null;
            break;
        }
      });
    }
  });
  
  self.postMessage({
    taskId,
    type: 'aggregateData',
    result: {
      groups: result,
      groupBy: groupBy,
      totalGroups: Object.keys(result).length,
      aggregatedAt: Date.now()
    }
  });
}

function validateData(taskId, data) {
  const { items, schema } = data;
  
  const validationResults = {
    valid: [],
    invalid: [],
    errors: []
  };
  
  items.forEach((item, index) => {
    const errors = [];
    
    // 验证必需字段
    if (schema.required) {
      schema.required.forEach(field => {
        if (!getNestedValue(item, field)) {
          errors.push(`Missing required field: ${field}`);
        }
      });
    }
    
    // 验证字段类型
    if (schema.fields) {
      Object.keys(schema.fields).forEach(field => {
        const value = getNestedValue(item, field);
        const fieldSchema = schema.fields[field];
        
        if (value != null && fieldSchema.type) {
          const actualType = Array.isArray(value) ? 'array' : typeof value;
          if (actualType !== fieldSchema.type) {
            errors.push(`Field ${field} should be ${fieldSchema.type}, got ${actualType}`);
          }
        }
      });
    }
    
    if (errors.length === 0) {
      validationResults.valid.push({ index, item });
    } else {
      validationResults.invalid.push({ index, item, errors });
      validationResults.errors.push(...errors);
    }
  });
  
  self.postMessage({
    taskId,
    type: 'validateData',
    result: {
      ...validationResults,
      totalItems: items.length,
      validCount: validationResults.valid.length,
      invalidCount: validationResults.invalid.length,
      validatedAt: Date.now()
    }
  });
}

function transformData(taskId, data) {
  const { items, transformations } = data;
  
  const transformedItems = items.map(item => {
    let transformed = { ...item };
    
    transformations.forEach(transform => {
      switch (transform.type) {
        case 'rename':
          if (transformed[transform.from]) {
            transformed[transform.to] = transformed[transform.from];
            delete transformed[transform.from];
          }
          break;
        case 'map':
          if (transformed[transform.field] && transform.mapping) {
            transformed[transform.field] = transform.mapping[transformed[transform.field]] || transformed[transform.field];
          }
          break;
        case 'calculate':
          transformed[transform.field] = calculateValue(transformed, transform.expression);
          break;
        case 'format':
          if (transformed[transform.field]) {
            transformed[transform.field] = formatValue(transformed[transform.field], transform.format);
          }
          break;
      }
    });
    
    return transformed;
  });
  
  self.postMessage({
    taskId,
    type: 'transformData',
    result: {
      items: transformedItems,
      transformations: transformations,
      transformedAt: Date.now()
    }
  });
}

// 工具函数
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => current && current[key], obj);
}

function generateHash(str) {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return Math.abs(hash).toString(36);
}

function generateTags(item) {
  const tags = [];
  if (item.category) tags.push(item.category);
  if (item.source) tags.push(item.source);
  if (item.width && item.height) {
    const ratio = item.width / item.height;
    if (ratio > 1.5) tags.push('landscape');
    else if (ratio < 0.7) tags.push('portrait');
    else tags.push('square');
  }
  return tags;
}

function categorizeItem(item) {
  if (item.category) return item.category;
  if (item.tags && item.tags.includes('nature')) return 'nature';
  if (item.tags && item.tags.includes('city')) return 'urban';
  return 'general';
}

function calculateValue(item, expression) {
  // 简单的表达式计算器
  try {
    return eval(expression.replace(/\{(\w+)\}/g, (match, field) => item[field] || 0));
  } catch (e) {
    return null;
  }
}

function formatValue(value, format) {
  switch (format) {
    case 'uppercase':
      return String(value).toUpperCase();
    case 'lowercase':
      return String(value).toLowerCase();
    case 'capitalize':
      return String(value).charAt(0).toUpperCase() + String(value).slice(1).toLowerCase();
    case 'date':
      return new Date(value).toLocaleDateString();
    case 'datetime':
      return new Date(value).toLocaleString();
    default:
      return value;
  }
}

// 错误处理
self.onerror = function(error) {
  console.error('Data Worker Error:', error);
  self.postMessage({
    type: 'error',
    error: error.message || 'Unknown worker error'
  });
};

// Worker状态报告
setInterval(() => {
  self.postMessage({
    type: 'heartbeat',
    timestamp: Date.now(),
    memory: self.performance && self.performance.memory ? {
      used: Math.round(self.performance.memory.usedJSHeapSize / 1024 / 1024),
      total: Math.round(self.performance.memory.totalJSHeapSize / 1024 / 1024)
    } : null
  });
}, 30000); // 每30秒发送一次心跳
