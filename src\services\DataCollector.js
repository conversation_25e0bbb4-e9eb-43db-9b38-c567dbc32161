import { ApiService } from './ApiService.js';
import { ImageProcessor } from '../utils/ImageProcessor.js';

export class DataCollector {
  constructor(workerManager) {
    this.workerManager = workerManager;
    this.apiService = new ApiService();
    this.imageProcessor = new ImageProcessor();
    this.isCollecting = false;
    this.collectionStats = {
      totalImages: 0,
      processingSpeed: 0,
      errors: 0,
      processedImages: 0,
      startTime: null
    };

    // 数据源配置
    this.dataSources = [
      {
        name: 'Unsplash',
        enabled: false,
        priority: 1
      },
      {
        name: 'Pixabay',
        enabled: false,
        priority: 2
      },
      {
        name: 'Pexels',
        enabled: false,
        priority: 3
      },
      {
        name: 'Mock Data',
        enabled: true, // 默认启用模拟数据
        priority: 4
      }
    ];

    // 采集配置
    this.collectionConfig = {
      batchSize: 20,
      maxImages: 100,
      enableImageProcessing: true,
      generateThumbnails: true,
      extractColors: true,
      analyzeQuality: true
    };
  }

  async startCollection() {
    if (this.isCollecting) {
      console.log('数据采集已在进行中');
      return;
    }

    console.log('🚀 开始数据采集...');
    this.isCollecting = true;
    
    try {
      // 重置统计
      this.resetStats();
      
      // 获取启用的数据源
      const enabledSources = this.dataSources.filter(source => source.enabled);
      
      if (enabledSources.length === 0) {
        throw new Error('没有启用的数据源');
      }

      // 并行采集数据
      const collectionPromises = enabledSources.map(source => 
        this.collectFromSource(source)
      );

      const results = await Promise.allSettled(collectionPromises);
      
      // 处理结果
      const allImages = [];
      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          allImages.push(...result.value);
          console.log(`✅ ${enabledSources[index].name} 采集完成: ${result.value.length} 张图片`);
        } else {
          console.error(`❌ ${enabledSources[index].name} 采集失败:`, result.reason);
          this.collectionStats.errors++;
        }
      });

      // 更新统计
      this.collectionStats.totalImages = allImages.length;
      
      // 通知主应用更新图库
      this.notifyCollectionComplete(allImages);
      
      console.log(`🎉 数据采集完成! 总计: ${allImages.length} 张图片`);
      
      return allImages;
      
    } catch (error) {
      console.error('❌ 数据采集失败:', error);
      throw error;
    } finally {
      this.isCollecting = false;
    }
  }

  async collectFromSource(source) {
    console.log(`📡 从 ${source.name} 采集数据...`);

    try {
      let rawImages = [];

      switch (source.name) {
        case 'Mock Data':
          rawImages = await this.collectMockData();
          break;
        case 'Unsplash':
          rawImages = await this.collectFromUnsplash();
          break;
        case 'Pixabay':
          rawImages = await this.collectFromPixabay();
          break;
        case 'Pexels':
          rawImages = await this.collectFromPexels();
          break;
        default:
          throw new Error(`未知的数据源: ${source.name}`);
      }

      // 处理图片数据
      if (this.collectionConfig.enableImageProcessing) {
        rawImages = await this.processImages(rawImages);
      }

      return rawImages;

    } catch (error) {
      console.error(`${source.name} 采集失败:`, error);
      throw error;
    }
  }

  async collectMockData() {
    // 模拟网络延迟
    await this.delay(1000 + Math.random() * 2000);
    
    const images = [];
    const imageCount = 20 + Math.floor(Math.random() * 30); // 20-50张图片
    
    const categories = ['nature', 'city', 'people', 'technology', 'art', 'food', 'travel'];
    const keywords = ['landscape', 'portrait', 'abstract', 'minimal', 'colorful', 'black-white'];
    
    for (let i = 0; i < imageCount; i++) {
      const category = categories[Math.floor(Math.random() * categories.length)];
      const keyword = keywords[Math.floor(Math.random() * keywords.length)];
      const width = 400 + Math.floor(Math.random() * 400); // 400-800
      const height = 300 + Math.floor(Math.random() * 500); // 300-800
      
      images.push({
        id: `mock_${Date.now()}_${i}`,
        url: `https://picsum.photos/${width}/${height}?random=${Date.now()}_${i}`,
        thumbnail: `https://picsum.photos/300/300?random=${Date.now()}_${i}`,
        title: `${category} ${keyword} ${i + 1}`,
        description: `A beautiful ${keyword} image in ${category} category`,
        category: category,
        tags: this.generateTags(category, keyword),
        width: width,
        height: height,
        source: 'Mock Data',
        createdAt: new Date(),
        metadata: {
          fileSize: Math.floor(Math.random() * 2000000) + 500000, // 0.5-2.5MB
          format: 'JPEG',
          quality: 85 + Math.floor(Math.random() * 15) // 85-100
        }
      });
      
      // 模拟处理进度
      if (i % 5 === 0) {
        this.updateProcessingSpeed(i / ((Date.now() - this.startTime) / 1000));
        await this.delay(100); // 小延迟以显示进度
      }
    }
    
    return images;
  }

  async collectFromUnsplash() {
    try {
      return await this.apiService.fetchFromUnsplash({
        per_page: this.collectionConfig.batchSize
      });
    } catch (error) {
      console.warn('Unsplash采集失败，使用模拟数据:', error.message);
      return [];
    }
  }

  async collectFromPixabay() {
    try {
      return await this.apiService.fetchFromPixabay({
        per_page: this.collectionConfig.batchSize
      });
    } catch (error) {
      console.warn('Pixabay采集失败，使用模拟数据:', error.message);
      return [];
    }
  }

  async collectFromPexels() {
    try {
      return await this.apiService.fetchFromPexels({
        per_page: this.collectionConfig.batchSize
      });
    } catch (error) {
      console.warn('Pexels采集失败，使用模拟数据:', error.message);
      return [];
    }
  }

  // 处理图片数据
  async processImages(images) {
    console.log(`🔄 开始处理 ${images.length} 张图片...`);

    const processedImages = [];
    const batchSize = 5; // 批量处理以避免过载

    for (let i = 0; i < images.length; i += batchSize) {
      const batch = images.slice(i, i + batchSize);
      const batchPromises = batch.map(image => this.processImage(image));

      try {
        const batchResults = await Promise.allSettled(batchPromises);

        batchResults.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            processedImages.push(result.value);
          } else {
            console.warn(`图片处理失败:`, batch[index].url, result.reason);
            // 保留原始图片数据
            processedImages.push(batch[index]);
          }
        });

        // 更新处理进度
        this.collectionStats.processedImages = processedImages.length;
        this.updateProcessingSpeed();

      } catch (error) {
        console.error('批量图片处理失败:', error);
        // 添加未处理的图片
        processedImages.push(...batch);
      }
    }

    console.log(`✅ 图片处理完成: ${processedImages.length}/${images.length}`);
    return processedImages;
  }

  async processImage(image) {
    try {
      const processedImage = { ...image };

      // 生成缩略图
      if (this.collectionConfig.generateThumbnails) {
        try {
          const thumbnailData = await this.imageProcessor.generateThumbnail(image.url, 300);
          processedImage.thumbnailGenerated = thumbnailData.url;
          processedImage.thumbnailSize = {
            width: thumbnailData.width,
            height: thumbnailData.height
          };
        } catch (error) {
          console.warn('缩略图生成失败:', image.url, error.message);
        }
      }

      // 提取主色调
      if (this.collectionConfig.extractColors) {
        try {
          const colors = await this.imageProcessor.extractDominantColors(image.thumbnail || image.url, 3);
          processedImage.dominantColors = colors;
        } catch (error) {
          console.warn('颜色提取失败:', image.url, error.message);
        }
      }

      // 分析图片质量
      if (this.collectionConfig.analyzeQuality) {
        try {
          const quality = await this.imageProcessor.analyzeImageQuality(image.thumbnail || image.url);
          processedImage.quality = quality;
        } catch (error) {
          console.warn('质量分析失败:', image.url, error.message);
        }
      }

      // 添加处理时间戳
      processedImage.processedAt = new Date();
      processedImage.processed = true;

      return processedImage;

    } catch (error) {
      console.error('图片处理失败:', image.url, error);
      return image; // 返回原始图片
    }
  }

  generateTags(category, keyword) {
    const baseTags = [category, keyword];
    const additionalTags = [
      'photography', 'digital', 'creative', 'visual', 'design',
      'artistic', 'modern', 'contemporary', 'beautiful', 'stunning'
    ];

    // 随机添加1-3个额外标签
    const extraCount = Math.floor(Math.random() * 3) + 1;
    for (let i = 0; i < extraCount; i++) {
      const tag = additionalTags[Math.floor(Math.random() * additionalTags.length)];
      if (!baseTags.includes(tag)) {
        baseTags.push(tag);
      }
    }

    return baseTags;
  }

  resetStats() {
    this.collectionStats = {
      totalImages: 0,
      processingSpeed: 0,
      errors: 0,
      processedImages: 0,
      startTime: Date.now()
    };
  }

  updateProcessingSpeed() {
    if (this.collectionStats.startTime) {
      const elapsed = (Date.now() - this.collectionStats.startTime) / 1000;
      const speed = this.collectionStats.processedImages / elapsed;
      this.collectionStats.processingSpeed = Math.round(speed * 10) / 10;
    }

    // 通知主应用更新统计
    if (window.app) {
      window.app.updateStats(this.collectionStats);
    }
  }

  notifyCollectionComplete(images) {
    // 通知主应用数据采集完成
    if (window.app && window.app.imageGallery) {
      window.app.imageGallery.addImages(images);
      window.app.updateStats(this.collectionStats);
    }

    // 触发自定义事件
    const event = new CustomEvent('dataCollectionComplete', {
      detail: {
        images: images,
        stats: this.collectionStats
      }
    });
    document.dispatchEvent(event);
  }

  // 配置数据源
  configureSource(sourceName, config) {
    const source = this.dataSources.find(s => s.name === sourceName);
    if (source) {
      Object.assign(source, config);
      console.log(`✅ ${sourceName} 配置已更新`);
    } else {
      console.error(`❌ 未找到数据源: ${sourceName}`);
    }
  }

  // 启用/禁用数据源
  toggleSource(sourceName, enabled) {
    const source = this.dataSources.find(s => s.name === sourceName);
    if (source) {
      source.enabled = enabled;
      console.log(`${enabled ? '✅' : '❌'} ${sourceName} ${enabled ? '已启用' : '已禁用'}`);
    }
  }

  // 获取采集统计
  getStats() {
    return { ...this.collectionStats };
  }

  // 停止采集
  stopCollection() {
    this.isCollecting = false;
    console.log('🛑 数据采集已停止');
  }

  // 配置API服务
  configureApiService(apiName, config) {
    this.apiService.configureApi(apiName, config);
  }

  // 获取API状态
  getApiStatus() {
    return this.apiService.getApiStatus();
  }

  // 更新采集配置
  updateCollectionConfig(config) {
    this.collectionConfig = {
      ...this.collectionConfig,
      ...config
    };
    console.log('✅ 采集配置已更新:', this.collectionConfig);
  }

  // 获取采集配置
  getCollectionConfig() {
    return { ...this.collectionConfig };
  }

  // 搜索图片
  async searchImages(query, source = 'all', params = {}) {
    try {
      console.log(`🔍 搜索图片: "${query}" 来源: ${source}`);

      const results = await this.apiService.searchImages(query, source, {
        per_page: this.collectionConfig.batchSize,
        ...params
      });

      // 处理搜索结果
      if (this.collectionConfig.enableImageProcessing && results.length > 0) {
        return await this.processImages(results);
      }

      return results;

    } catch (error) {
      console.error('图片搜索失败:', error);
      throw error;
    }
  }

  // 批量采集
  async batchCollect(sources = null, maxImages = null) {
    const enabledSources = sources || this.dataSources
      .filter(source => source.enabled)
      .sort((a, b) => a.priority - b.priority);

    if (enabledSources.length === 0) {
      throw new Error('没有启用的数据源');
    }

    const targetCount = maxImages || this.collectionConfig.maxImages;
    const allImages = [];

    for (const source of enabledSources) {
      if (allImages.length >= targetCount) break;

      try {
        const images = await this.collectFromSource(source);
        const remainingSlots = targetCount - allImages.length;
        const imagesToAdd = images.slice(0, remainingSlots);

        allImages.push(...imagesToAdd);

        console.log(`✅ ${source.name}: ${imagesToAdd.length} 张图片`);

      } catch (error) {
        console.error(`❌ ${source.name} 采集失败:`, error);
        this.collectionStats.errors++;
      }
    }

    return allImages;
  }

  // 获取数据源状态
  getDataSourceStatus() {
    return this.dataSources.map(source => ({
      ...source,
      apiStatus: source.name !== 'Mock Data' ?
        this.apiService.getApiStatus()[source.name.toLowerCase()] :
        { configured: true, rateLimit: { used: 0, limit: Infinity } }
    }));
  }

  // 清理资源
  dispose() {
    if (this.imageProcessor) {
      this.imageProcessor.dispose();
    }

    this.isCollecting = false;
    console.log('🧹 DataCollector 资源已清理');
  }

  // 工具方法：延迟
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
