// 图片处理Worker
self.onmessage = function(event) {
  const { taskId, type, data } = event.data;
  
  try {
    switch (type) {
      case 'processImage':
        processImage(taskId, data);
        break;
      case 'generateThumbnail':
        generateThumbnail(taskId, data);
        break;
      case 'extractMetadata':
        extractMetadata(taskId, data);
        break;
      case 'analyzeColors':
        analyzeColors(taskId, data);
        break;
      case 'compressImage':
        compressImage(taskId, data);
        break;
      case 'applyFilter':
        applyFilter(taskId, data);
        break;
      default:
        throw new Error('未知的图片处理任务类型: ' + type);
    }
  } catch (error) {
    self.postMessage({
      taskId,
      type: 'error',
      error: error.message
    });
  }
};

function processImage(taskId, data) {
  const { imageUrl, filters, options } = data;
  
  // 模拟图片处理时间
  const processingTime = 500 + Math.random() * 1000;
  
  setTimeout(() => {
    self.postMessage({
      taskId,
      type: 'processImage',
      result: {
        processedUrl: imageUrl + '?processed=true',
        filters: filters,
        processingTime: processingTime,
        timestamp: Date.now(),
        options: options
      }
    });
  }, processingTime);
}

function generateThumbnail(taskId, data) {
  const { imageUrl, size, quality } = data;
  
  // 模拟缩略图生成
  setTimeout(() => {
    // 生成不同尺寸的缩略图
    const thumbnails = {
      small: `${imageUrl}?w=150&h=150&q=${quality || 80}`,
      medium: `${imageUrl}?w=300&h=300&q=${quality || 80}`,
      large: `${imageUrl}?w=${size || 500}&h=${size || 500}&q=${quality || 80}`
    };
    
    self.postMessage({
      taskId,
      type: 'generateThumbnail',
      result: {
        thumbnails: thumbnails,
        originalUrl: imageUrl,
        size: size || 500,
        quality: quality || 80,
        generatedAt: Date.now()
      }
    });
  }, 200 + Math.random() * 300);
}

function extractMetadata(taskId, data) {
  const { imageUrl } = data;
  
  // 模拟元数据提取
  setTimeout(() => {
    const metadata = {
      url: imageUrl,
      width: 800 + Math.floor(Math.random() * 1200),
      height: 600 + Math.floor(Math.random() * 800),
      format: ['JPEG', 'PNG', 'WebP'][Math.floor(Math.random() * 3)],
      fileSize: Math.floor(Math.random() * 5000000) + 500000, // 0.5-5.5MB
      colorSpace: 'sRGB',
      hasAlpha: Math.random() > 0.7,
      created: new Date(Date.now() - Math.random() * 86400000 * 365).toISOString(), // 随机过去一年内
      camera: {
        make: ['Canon', 'Nikon', 'Sony', 'Apple'][Math.floor(Math.random() * 4)],
        model: 'Camera Model ' + Math.floor(Math.random() * 1000),
        iso: [100, 200, 400, 800, 1600][Math.floor(Math.random() * 5)],
        aperture: 'f/' + (1.4 + Math.random() * 10).toFixed(1),
        shutterSpeed: '1/' + Math.floor(Math.random() * 1000 + 60),
        focalLength: Math.floor(Math.random() * 200 + 24) + 'mm'
      },
      gps: Math.random() > 0.5 ? {
        latitude: (Math.random() * 180 - 90).toFixed(6),
        longitude: (Math.random() * 360 - 180).toFixed(6)
      } : null
    };
    
    self.postMessage({
      taskId,
      type: 'extractMetadata',
      result: metadata
    });
  }, 100 + Math.random() * 200);
}

function analyzeColors(taskId, data) {
  const { imageUrl, colorCount } = data;
  
  // 模拟颜色分析
  setTimeout(() => {
    const colors = [];
    const colorNames = [
      { name: '深蓝', hex: '#1e3a8a' },
      { name: '翠绿', hex: '#059669' },
      { name: '暖红', hex: '#dc2626' },
      { name: '金黄', hex: '#d97706' },
      { name: '紫罗兰', hex: '#7c3aed' },
      { name: '粉红', hex: '#e11d48' },
      { name: '青色', hex: '#0891b2' },
      { name: '橙色', hex: '#ea580c' }
    ];
    
    for (let i = 0; i < (colorCount || 5); i++) {
      const colorInfo = colorNames[Math.floor(Math.random() * colorNames.length)];
      colors.push({
        hex: colorInfo.hex,
        name: colorInfo.name,
        rgb: hexToRgb(colorInfo.hex),
        percentage: Math.floor(Math.random() * 30 + 5), // 5-35%
        dominance: Math.random()
      });
    }
    
    // 按百分比排序
    colors.sort((a, b) => b.percentage - a.percentage);
    
    self.postMessage({
      taskId,
      type: 'analyzeColors',
      result: {
        colors: colors,
        totalColors: colors.length,
        dominantColor: colors[0],
        palette: colors.map(c => c.hex),
        analyzedAt: Date.now()
      }
    });
  }, 300 + Math.random() * 500);
}

function compressImage(taskId, data) {
  const { imageUrl, quality, format } = data;
  
  // 模拟图片压缩
  setTimeout(() => {
    const originalSize = Math.floor(Math.random() * 5000000) + 1000000; // 1-6MB
    const compressionRatio = quality ? (quality / 100) : 0.8;
    const compressedSize = Math.floor(originalSize * compressionRatio);
    
    self.postMessage({
      taskId,
      type: 'compressImage',
      result: {
        originalUrl: imageUrl,
        compressedUrl: `${imageUrl}?compressed=true&q=${quality || 80}`,
        originalSize: originalSize,
        compressedSize: compressedSize,
        compressionRatio: ((originalSize - compressedSize) / originalSize * 100).toFixed(1) + '%',
        quality: quality || 80,
        format: format || 'JPEG',
        compressedAt: Date.now()
      }
    });
  }, 400 + Math.random() * 600);
}

function applyFilter(taskId, data) {
  const { imageUrl, filterType, intensity } = data;
  
  // 模拟滤镜应用
  setTimeout(() => {
    const filters = {
      grayscale: '灰度',
      sepia: '复古',
      blur: '模糊',
      sharpen: '锐化',
      brightness: '亮度',
      contrast: '对比度',
      saturation: '饱和度',
      vintage: '怀旧',
      cool: '冷色调',
      warm: '暖色调'
    };
    
    self.postMessage({
      taskId,
      type: 'applyFilter',
      result: {
        originalUrl: imageUrl,
        filteredUrl: `${imageUrl}?filter=${filterType}&intensity=${intensity || 1}`,
        filterType: filterType,
        filterName: filters[filterType] || filterType,
        intensity: intensity || 1,
        appliedAt: Date.now(),
        processingTime: 200 + Math.random() * 400
      }
    });
  }, 200 + Math.random() * 400);
}

// 工具函数
function hexToRgb(hex) {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}

// 错误处理
self.onerror = function(error) {
  console.error('Image Worker Error:', error);
  self.postMessage({
    type: 'error',
    error: error.message || 'Unknown worker error'
  });
};

// Worker状态报告
setInterval(() => {
  self.postMessage({
    type: 'heartbeat',
    timestamp: Date.now(),
    memory: self.performance && self.performance.memory ? {
      used: Math.round(self.performance.memory.usedJSHeapSize / 1024 / 1024),
      total: Math.round(self.performance.memory.totalJSHeapSize / 1024 / 1024)
    } : null
  });
}, 30000); // 每30秒发送一次心跳
