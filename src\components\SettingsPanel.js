import { animate, utils } from 'animejs';

export class SettingsPanel {
  constructor(dataCollector, animationController) {
    this.dataCollector = dataCollector;
    this.animationController = animationController;
    this.isVisible = false;
    this.settings = this.loadSettings();

    this.init();
  }

  init() {
    this.createSettingsPanel();
    this.bindEvents();
    this.updateUI();
  }

  createSettingsPanel() {
    this.panel = document.createElement('div');
    this.panel.id = 'settings-panel';
    this.panel.className = 'fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 z-50 hidden';

    this.panel.innerHTML = `
      <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
          <!-- Header -->
          <div class="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">系统设置</h2>
            <button id="close-settings" class="text-gray-400 hover:text-gray-600 transition-colors">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <!-- Content -->
          <div class="flex">
            <!-- Sidebar -->
            <div class="w-64 bg-gray-50 border-r border-gray-200">
              <nav class="p-4 space-y-2">
                <button class="settings-tab w-full text-left px-3 py-2 rounded-lg text-sm font-medium transition-colors bg-primary-100 text-primary-700" data-tab="api">
                  API配置
                </button>
                <button class="settings-tab w-full text-left px-3 py-2 rounded-lg text-sm font-medium transition-colors text-gray-600 hover:bg-gray-100" data-tab="collection">
                  采集设置
                </button>
                <button class="settings-tab w-full text-left px-3 py-2 rounded-lg text-sm font-medium transition-colors text-gray-600 hover:bg-gray-100" data-tab="display">
                  显示设置
                </button>
                <button class="settings-tab w-full text-left px-3 py-2 rounded-lg text-sm font-medium transition-colors text-gray-600 hover:bg-gray-100" data-tab="performance">
                  性能设置
                </button>
                <button class="settings-tab w-full text-left px-3 py-2 rounded-lg text-sm font-medium transition-colors text-gray-600 hover:bg-gray-100" data-tab="about">
                  关于
                </button>
              </nav>
            </div>

            <!-- Main Content -->
            <div class="flex-1 p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
              <!-- API配置 -->
              <div id="tab-api" class="settings-content">
                <h3 class="text-lg font-medium text-gray-900 mb-4">API配置</h3>

                <div class="space-y-6">
                  <!-- Unsplash -->
                  <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                      <h4 class="font-medium text-gray-900">Unsplash</h4>
                      <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="unsplash-enabled" class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                      </label>
                    </div>
                    <div class="space-y-3">
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Access Key</label>
                        <input type="password" id="unsplash-key" placeholder="输入Unsplash Access Key"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                      </div>
                      <div class="text-xs text-gray-500">
                        <a href="https://unsplash.com/developers" target="_blank" class="text-primary-600 hover:text-primary-700">获取API密钥</a>
                      </div>
                    </div>
                  </div>

                  <!-- Pixabay -->
                  <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                      <h4 class="font-medium text-gray-900">Pixabay</h4>
                      <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="pixabay-enabled" class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                      </label>
                    </div>
                    <div class="space-y-3">
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">API Key</label>
                        <input type="password" id="pixabay-key" placeholder="输入Pixabay API Key"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                      </div>
                      <div class="text-xs text-gray-500">
                        <a href="https://pixabay.com/api/docs/" target="_blank" class="text-primary-600 hover:text-primary-700">获取API密钥</a>
                      </div>
                    </div>
                  </div>

                  <!-- Pexels -->
                  <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                      <h4 class="font-medium text-gray-900">Pexels</h4>
                      <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="pexels-enabled" class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                      </label>
                    </div>
                    <div class="space-y-3">
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">API Key</label>
                        <input type="password" id="pexels-key" placeholder="输入Pexels API Key"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                      </div>
                      <div class="text-xs text-gray-500">
                        <a href="https://www.pexels.com/api/" target="_blank" class="text-primary-600 hover:text-primary-700">获取API密钥</a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 采集设置 -->
              <div id="tab-collection" class="settings-content hidden">
                <h3 class="text-lg font-medium text-gray-900 mb-4">采集设置</h3>

                <div class="space-y-6">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">批次大小</label>
                      <input type="number" id="batch-size" min="5" max="50" value="20"
                             class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500">
                      <p class="text-xs text-gray-500 mt-1">每次采集的图片数量</p>
                    </div>

                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">最大图片数</label>
                      <input type="number" id="max-images" min="10" max="500" value="100"
                             class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500">
                      <p class="text-xs text-gray-500 mt-1">单次采集的最大图片数量</p>
                    </div>
                  </div>

                  <div class="space-y-4">
                    <div class="flex items-center justify-between">
                      <div>
                        <h4 class="font-medium text-gray-900">启用图片处理</h4>
                        <p class="text-sm text-gray-500">自动处理和分析采集的图片</p>
                      </div>
                      <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="enable-processing" class="sr-only peer" checked>
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                      </label>
                    </div>

                    <div class="flex items-center justify-between">
                      <div>
                        <h4 class="font-medium text-gray-900">生成缩略图</h4>
                        <p class="text-sm text-gray-500">为图片生成优化的缩略图</p>
                      </div>
                      <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="generate-thumbnails" class="sr-only peer" checked>
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                      </label>
                    </div>

                    <div class="flex items-center justify-between">
                      <div>
                        <h4 class="font-medium text-gray-900">提取主色调</h4>
                        <p class="text-sm text-gray-500">分析图片的主要颜色</p>
                      </div>
                      <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="extract-colors" class="sr-only peer" checked>
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                      </label>
                    </div>

                    <div class="flex items-center justify-between">
                      <div>
                        <h4 class="font-medium text-gray-900">质量分析</h4>
                        <p class="text-sm text-gray-500">分析图片质量和特征</p>
                      </div>
                      <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="analyze-quality" class="sr-only peer" checked>
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 显示设置 -->
              <div id="tab-display" class="settings-content hidden">
                <h3 class="text-lg font-medium text-gray-900 mb-4">显示设置</h3>

                <div class="space-y-6">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">默认视图模式</label>
                    <select id="default-view-mode" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500">
                      <option value="grid">网格视图</option>
                      <option value="list">列表视图</option>
                      <option value="masonry">瀑布流</option>
                    </select>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">动画速度</label>
                    <input type="range" id="animation-speed" min="0.5" max="2" step="0.1" value="1"
                           class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                    <div class="flex justify-between text-xs text-gray-500 mt-1">
                      <span>慢</span>
                      <span id="speed-value">1.0x</span>
                      <span>快</span>
                    </div>
                  </div>

                  <div class="flex items-center justify-between">
                    <div>
                      <h4 class="font-medium text-gray-900">减少动画</h4>
                      <p class="text-sm text-gray-500">为性能较低的设备减少动画效果</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" id="reduce-animations" class="sr-only peer">
                      <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                    </label>
                  </div>

                  <div class="flex items-center justify-between">
                    <div>
                      <h4 class="font-medium text-gray-900">自动刷新</h4>
                      <p class="text-sm text-gray-500">定期自动刷新图库内容</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" id="auto-refresh" class="sr-only peer">
                      <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                    </label>
                  </div>
                </div>
              </div>

              <!-- 性能设置 -->
              <div id="tab-performance" class="settings-content hidden">
                <h3 class="text-lg font-medium text-gray-900 mb-4">性能设置</h3>

                <div class="space-y-6">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Worker数量</label>
                    <input type="number" id="worker-count" min="1" max="8" value="4"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500">
                    <p class="text-xs text-gray-500 mt-1">并行处理的Worker数量（建议不超过CPU核心数）</p>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">缓存大小 (MB)</label>
                    <input type="number" id="cache-size" min="50" max="500" value="100"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500">
                    <p class="text-xs text-gray-500 mt-1">图片缓存的最大大小</p>
                  </div>

                  <div class="flex items-center justify-between">
                    <div>
                      <h4 class="font-medium text-gray-900">启用图片懒加载</h4>
                      <p class="text-sm text-gray-500">仅在需要时加载图片以提升性能</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" id="lazy-loading" class="sr-only peer" checked>
                      <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                    </label>
                  </div>

                  <div class="flex items-center justify-between">
                    <div>
                      <h4 class="font-medium text-gray-900">预加载下一批</h4>
                      <p class="text-sm text-gray-500">提前加载下一批图片以提升体验</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" id="preload-next" class="sr-only peer" checked>
                      <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                    </label>
                  </div>
                </div>
              </div>

              <!-- 关于 -->
              <div id="tab-about" class="settings-content hidden">
                <h3 class="text-lg font-medium text-gray-900 mb-4">关于</h3>

                <div class="space-y-6">
                  <div class="text-center">
                    <h4 class="text-xl font-semibold text-gray-900 mb-2">Liu 图库</h4>
                    <p class="text-gray-600 mb-4">基于现代前端技术的智能图片采集与展示平台</p>
                    <div class="inline-flex items-center px-3 py-1 bg-primary-100 text-primary-800 rounded-full text-sm">
                      版本 1.0.0
                    </div>
                  </div>

                  <div class="border-t border-gray-200 pt-6">
                    <h5 class="font-medium text-gray-900 mb-3">技术栈</h5>
                    <div class="grid grid-cols-2 gap-3 text-sm">
                      <div class="flex items-center space-x-2">
                        <span class="w-2 h-2 bg-orange-500 rounded-full"></span>
                        <span>HTML5 + Vite</span>
                      </div>
                      <div class="flex items-center space-x-2">
                        <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                        <span>Tailwind CSS</span>
                      </div>
                      <div class="flex items-center space-x-2">
                        <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                        <span>Anime.js</span>
                      </div>
                      <div class="flex items-center space-x-2">
                        <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                        <span>Web Workers</span>
                      </div>
                    </div>
                  </div>

                  <div class="border-t border-gray-200 pt-6">
                    <h5 class="font-medium text-gray-900 mb-3">系统信息</h5>
                    <div class="space-y-2 text-sm text-gray-600">
                      <div class="flex justify-between">
                        <span>浏览器:</span>
                        <span id="browser-info">-</span>
                      </div>
                      <div class="flex justify-between">
                        <span>CPU核心数:</span>
                        <span id="cpu-cores">-</span>
                      </div>
                      <div class="flex justify-between">
                        <span>内存使用:</span>
                        <span id="memory-usage">-</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Footer -->
          <div class="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
            <button id="reset-settings" class="text-gray-600 hover:text-gray-800 text-sm">
              重置为默认设置
            </button>
            <div class="flex space-x-3">
              <button id="cancel-settings" class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors">
                取消
              </button>
              <button id="save-settings" class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors">
                保存设置
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(this.panel);
  }

  bindEvents() {
    // 关闭按钮
    const closeBtn = this.panel.querySelector('#close-settings');
    closeBtn.addEventListener('click', () => this.hide());

    // 取消按钮
    const cancelBtn = this.panel.querySelector('#cancel-settings');
    cancelBtn.addEventListener('click', () => this.hide());

    // 保存按钮
    const saveBtn = this.panel.querySelector('#save-settings');
    saveBtn.addEventListener('click', () => this.saveSettings());

    // 重置按钮
    const resetBtn = this.panel.querySelector('#reset-settings');
    resetBtn.addEventListener('click', () => this.resetSettings());

    // 标签页切换
    const tabs = this.panel.querySelectorAll('.settings-tab');
    tabs.forEach(tab => {
      tab.addEventListener('click', () => this.switchTab(tab.dataset.tab));
    });

    // 动画速度滑块
    const speedSlider = this.panel.querySelector('#animation-speed');
    const speedValue = this.panel.querySelector('#speed-value');
    speedSlider.addEventListener('input', (e) => {
      speedValue.textContent = `${parseFloat(e.target.value).toFixed(1)}x`;
    });

    // 点击背景关闭
    this.panel.addEventListener('click', (e) => {
      if (e.target === this.panel) this.hide();
    });

    // ESC键关闭
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isVisible) {
        this.hide();
      }
    });
  }

  switchTab(tabName) {
    // 更新标签页状态
    const tabs = this.panel.querySelectorAll('.settings-tab');
    tabs.forEach(tab => {
      if (tab.dataset.tab === tabName) {
        tab.className = 'settings-tab w-full text-left px-3 py-2 rounded-lg text-sm font-medium transition-colors bg-primary-100 text-primary-700';
      } else {
        tab.className = 'settings-tab w-full text-left px-3 py-2 rounded-lg text-sm font-medium transition-colors text-gray-600 hover:bg-gray-100';
      }
    });

    // 显示对应内容
    const contents = this.panel.querySelectorAll('.settings-content');
    contents.forEach(content => {
      if (content.id === `tab-${tabName}`) {
        content.classList.remove('hidden');
      } else {
        content.classList.add('hidden');
      }
    });
  }

  show() {
    if (this.isVisible) return;

    this.isVisible = true;
    this.panel.classList.remove('hidden');
    this.updateSystemInfo();

    // 入场动画
    const content = this.panel.querySelector('.bg-white');
    content.style.transform = 'scale(0.95)';
    content.style.opacity = '0';

    animate([
      {
        targets: this.panel,
        opacity: [0, 1],
        duration: 200,
        ease: 'easeOutQuad'
      },
      {
        targets: content,
        scale: [0.95, 1],
        opacity: [0, 1],
        duration: 300,
        delay: 100,
        ease: 'easeOutBack'
      }
    ]);
  }

  hide() {
    if (!this.isVisible) return;

    const content = this.panel.querySelector('.bg-white');

    animate([
      {
        targets: content,
        scale: [1, 0.95],
        opacity: [1, 0],
        duration: 200,
        ease: 'easeInQuad'
      },
      {
        targets: this.panel,
        opacity: [1, 0],
        duration: 200,
        delay: 100,
        ease: 'easeInQuad',
        complete: () => {
          this.panel.classList.add('hidden');
          this.isVisible = false;
        }
      }
    ]);
  }

  updateUI() {
    // 更新API配置
    this.panel.querySelector('#unsplash-enabled').checked = this.settings.apis.unsplash.enabled;
    this.panel.querySelector('#unsplash-key').value = this.settings.apis.unsplash.accessKey || '';

    this.panel.querySelector('#pixabay-enabled').checked = this.settings.apis.pixabay.enabled;
    this.panel.querySelector('#pixabay-key').value = this.settings.apis.pixabay.apiKey || '';

    this.panel.querySelector('#pexels-enabled').checked = this.settings.apis.pexels.enabled;
    this.panel.querySelector('#pexels-key').value = this.settings.apis.pexels.apiKey || '';

    // 更新采集设置
    this.panel.querySelector('#batch-size').value = this.settings.collection.batchSize;
    this.panel.querySelector('#max-images').value = this.settings.collection.maxImages;
    this.panel.querySelector('#enable-processing').checked = this.settings.collection.enableImageProcessing;
    this.panel.querySelector('#generate-thumbnails').checked = this.settings.collection.generateThumbnails;
    this.panel.querySelector('#extract-colors').checked = this.settings.collection.extractColors;
    this.panel.querySelector('#analyze-quality').checked = this.settings.collection.analyzeQuality;

    // 更新显示设置
    this.panel.querySelector('#default-view-mode').value = this.settings.display.defaultViewMode;
    this.panel.querySelector('#animation-speed').value = this.settings.display.animationSpeed;
    this.panel.querySelector('#speed-value').textContent = `${this.settings.display.animationSpeed.toFixed(1)}x`;
    this.panel.querySelector('#reduce-animations').checked = this.settings.display.reduceAnimations;
    this.panel.querySelector('#auto-refresh').checked = this.settings.display.autoRefresh;

    // 更新性能设置
    this.panel.querySelector('#worker-count').value = this.settings.performance.workerCount;
    this.panel.querySelector('#cache-size').value = this.settings.performance.cacheSize;
    this.panel.querySelector('#lazy-loading').checked = this.settings.performance.lazyLoading;
    this.panel.querySelector('#preload-next').checked = this.settings.performance.preloadNext;
  }

  saveSettings() {
    try {
      // 收集API设置
      this.settings.apis.unsplash.enabled = this.panel.querySelector('#unsplash-enabled').checked;
      this.settings.apis.unsplash.accessKey = this.panel.querySelector('#unsplash-key').value;

      this.settings.apis.pixabay.enabled = this.panel.querySelector('#pixabay-enabled').checked;
      this.settings.apis.pixabay.apiKey = this.panel.querySelector('#pixabay-key').value;

      this.settings.apis.pexels.enabled = this.panel.querySelector('#pexels-enabled').checked;
      this.settings.apis.pexels.apiKey = this.panel.querySelector('#pexels-key').value;

      // 收集采集设置
      this.settings.collection.batchSize = parseInt(this.panel.querySelector('#batch-size').value);
      this.settings.collection.maxImages = parseInt(this.panel.querySelector('#max-images').value);
      this.settings.collection.enableImageProcessing = this.panel.querySelector('#enable-processing').checked;
      this.settings.collection.generateThumbnails = this.panel.querySelector('#generate-thumbnails').checked;
      this.settings.collection.extractColors = this.panel.querySelector('#extract-colors').checked;
      this.settings.collection.analyzeQuality = this.panel.querySelector('#analyze-quality').checked;

      // 收集显示设置
      this.settings.display.defaultViewMode = this.panel.querySelector('#default-view-mode').value;
      this.settings.display.animationSpeed = parseFloat(this.panel.querySelector('#animation-speed').value);
      this.settings.display.reduceAnimations = this.panel.querySelector('#reduce-animations').checked;
      this.settings.display.autoRefresh = this.panel.querySelector('#auto-refresh').checked;

      // 收集性能设置
      this.settings.performance.workerCount = parseInt(this.panel.querySelector('#worker-count').value);
      this.settings.performance.cacheSize = parseInt(this.panel.querySelector('#cache-size').value);
      this.settings.performance.lazyLoading = this.panel.querySelector('#lazy-loading').checked;
      this.settings.performance.preloadNext = this.panel.querySelector('#preload-next').checked;

      // 应用设置
      this.applySettings();

      // 保存到本地存储
      this.saveToStorage();

      // 显示成功消息
      this.showSuccess('设置已保存');

      // 关闭面板
      this.hide();

    } catch (error) {
      console.error('保存设置失败:', error);
      this.showError('保存设置失败，请重试');
    }
  }

  applySettings() {
    // 应用API设置
    if (this.settings.apis.unsplash.enabled && this.settings.apis.unsplash.accessKey) {
      this.dataCollector.configureApiService('unsplash', {
        accessKey: this.settings.apis.unsplash.accessKey
      });
      this.dataCollector.toggleSource('Unsplash', true);
    } else {
      this.dataCollector.toggleSource('Unsplash', false);
    }

    if (this.settings.apis.pixabay.enabled && this.settings.apis.pixabay.apiKey) {
      this.dataCollector.configureApiService('pixabay', {
        apiKey: this.settings.apis.pixabay.apiKey
      });
      this.dataCollector.toggleSource('Pixabay', true);
    } else {
      this.dataCollector.toggleSource('Pixabay', false);
    }

    if (this.settings.apis.pexels.enabled && this.settings.apis.pexels.apiKey) {
      this.dataCollector.configureApiService('pexels', {
        apiKey: this.settings.apis.pexels.apiKey
      });
      this.dataCollector.toggleSource('Pexels', true);
    } else {
      this.dataCollector.toggleSource('Pexels', false);
    }

    // 应用采集设置
    this.dataCollector.updateCollectionConfig({
      batchSize: this.settings.collection.batchSize,
      maxImages: this.settings.collection.maxImages,
      enableImageProcessing: this.settings.collection.enableImageProcessing,
      generateThumbnails: this.settings.collection.generateThumbnails,
      extractColors: this.settings.collection.extractColors,
      analyzeQuality: this.settings.collection.analyzeQuality
    });

    // 应用显示设置
    if (window.app && window.app.imageGallery) {
      window.app.imageGallery.setViewMode(this.settings.display.defaultViewMode);
    }

    // 应用动画设置
    if (this.animationController) {
      // 这里可以设置动画控制器的全局设置
      document.documentElement.style.setProperty('--animation-speed', this.settings.display.animationSpeed);

      if (this.settings.display.reduceAnimations) {
        document.documentElement.classList.add('reduce-motion');
      } else {
        document.documentElement.classList.remove('reduce-motion');
      }
    }
  }

  resetSettings() {
    if (confirm('确定要重置所有设置为默认值吗？')) {
      this.settings = this.getDefaultSettings();
      this.updateUI();
      this.showSuccess('设置已重置为默认值');
    }
  }

  loadSettings() {
    try {
      const saved = localStorage.getItem('appSettings');
      if (saved) {
        const parsed = JSON.parse(saved);
        return { ...this.getDefaultSettings(), ...parsed };
      }
    } catch (error) {
      console.warn('加载设置失败:', error);
    }

    return this.getDefaultSettings();
  }

  saveToStorage() {
    try {
      localStorage.setItem('appSettings', JSON.stringify(this.settings));
    } catch (error) {
      console.warn('保存设置到本地存储失败:', error);
    }
  }

  getDefaultSettings() {
    return {
      apis: {
        unsplash: {
          enabled: false,
          accessKey: null
        },
        pixabay: {
          enabled: false,
          apiKey: null
        },
        pexels: {
          enabled: false,
          apiKey: null
        }
      },
      collection: {
        batchSize: 20,
        maxImages: 100,
        enableImageProcessing: true,
        generateThumbnails: true,
        extractColors: true,
        analyzeQuality: true
      },
      display: {
        defaultViewMode: 'grid',
        animationSpeed: 1.0,
        reduceAnimations: false,
        autoRefresh: false
      },
      performance: {
        workerCount: navigator.hardwareConcurrency || 4,
        cacheSize: 100,
        lazyLoading: true,
        preloadNext: true
      }
    };
  }

  updateSystemInfo() {
    // 更新浏览器信息
    const browserInfo = this.panel.querySelector('#browser-info');
    browserInfo.textContent = navigator.userAgent.split(' ').slice(-2).join(' ');

    // 更新CPU核心数
    const cpuCores = this.panel.querySelector('#cpu-cores');
    cpuCores.textContent = navigator.hardwareConcurrency || '未知';

    // 更新内存使用情况
    const memoryUsage = this.panel.querySelector('#memory-usage');
    if (performance.memory) {
      const used = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
      const total = Math.round(performance.memory.totalJSHeapSize / 1024 / 1024);
      memoryUsage.textContent = `${used}MB / ${total}MB`;
    } else {
      memoryUsage.textContent = '不支持';
    }
  }

  showSuccess(message) {
    console.log('✅', message);
    // 可以扩展为更好的通知系统
  }

  showError(message) {
    console.error('❌', message);
    alert(message);
  }

  // 获取当前设置
  getSettings() {
    return { ...this.settings };
  }

  // 更新特定设置
  updateSetting(path, value) {
    const keys = path.split('.');
    let current = this.settings;

    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) {
        current[keys[i]] = {};
      }
      current = current[keys[i]];
    }

    current[keys[keys.length - 1]] = value;
    this.saveToStorage();
  }
}