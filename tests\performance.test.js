// 性能测试
class PerformanceTest {
  constructor() {
    this.metrics = {};
    this.benchmarks = {};
    this.app = null;
  }

  async runPerformanceTests() {
    console.log('⚡ 开始性能测试...');
    
    try {
      // 等待应用初始化
      await this.waitForApp();
      
      // 运行各项性能测试
      await this.testAppStartupTime();
      await this.testDataCollectionPerformance();
      await this.testImageRenderingPerformance();
      await this.testWorkerPerformance();
      await this.testAnimationPerformance();
      await this.testMemoryUsage();
      
      // 输出性能报告
      this.generatePerformanceReport();
      
    } catch (error) {
      console.error('❌ 性能测试失败:', error);
    }
  }

  async waitForApp() {
    return new Promise((resolve, reject) => {
      const checkApp = () => {
        if (window.app && window.app.isInitialized) {
          this.app = window.app;
          resolve();
        } else if (Date.now() - startTime > 10000) {
          reject(new Error('应用初始化超时'));
        } else {
          setTimeout(checkApp, 100);
        }
      };
      
      const startTime = Date.now();
      checkApp();
    });
  }

  async testAppStartupTime() {
    console.log('测试应用启动时间...');
    
    // 从页面加载到应用初始化完成的时间
    const navigationStart = performance.timing.navigationStart;
    const loadComplete = performance.timing.loadEventEnd;
    const appInitTime = Date.now();
    
    this.metrics.pageLoadTime = loadComplete - navigationStart;
    this.metrics.appInitTime = appInitTime - loadComplete;
    this.metrics.totalStartupTime = appInitTime - navigationStart;
    
    // 基准值（毫秒）
    this.benchmarks.pageLoadTime = 2000;
    this.benchmarks.appInitTime = 1000;
    this.benchmarks.totalStartupTime = 3000;
    
    console.log(`页面加载时间: ${this.metrics.pageLoadTime}ms`);
    console.log(`应用初始化时间: ${this.metrics.appInitTime}ms`);
    console.log(`总启动时间: ${this.metrics.totalStartupTime}ms`);
  }

  async testDataCollectionPerformance() {
    console.log('测试数据采集性能...');
    
    const startTime = performance.now();
    
    try {
      // 测试生成大量模拟数据
      const dataCollector = this.app.dataCollector;
      const originalBatchSize = dataCollector.collectionConfig.batchSize;
      
      // 临时增加批次大小进行压力测试
      dataCollector.updateCollectionConfig({ batchSize: 100 });
      
      const images = await dataCollector.collectMockData();
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      this.metrics.dataCollectionTime = duration;
      this.metrics.dataCollectionThroughput = images.length / (duration / 1000); // 每秒处理的图片数
      
      // 恢复原配置
      dataCollector.updateCollectionConfig({ batchSize: originalBatchSize });
      
      // 基准值
      this.benchmarks.dataCollectionTime = 5000; // 5秒内完成
      this.benchmarks.dataCollectionThroughput = 20; // 每秒至少20张
      
      console.log(`数据采集时间: ${duration.toFixed(2)}ms`);
      console.log(`数据采集吞吐量: ${this.metrics.dataCollectionThroughput.toFixed(2)} 图片/秒`);
      
    } catch (error) {
      console.error('数据采集性能测试失败:', error);
    }
  }

  async testImageRenderingPerformance() {
    console.log('测试图片渲染性能...');
    
    const startTime = performance.now();
    
    try {
      const imageGallery = this.app.imageGallery;
      
      // 生成大量测试图片
      const testImages = [];
      for (let i = 0; i < 50; i++) {
        testImages.push({
          id: `perf_test_${i}`,
          url: `https://picsum.photos/300/200?random=${i}`,
          thumbnail: `https://picsum.photos/150/150?random=${i}`,
          title: `性能测试图片 ${i + 1}`,
          tags: ['performance', 'test']
        });
      }
      
      // 测试渲染时间
      await imageGallery.addImages(testImages);
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      this.metrics.imageRenderingTime = duration;
      this.metrics.imageRenderingThroughput = testImages.length / (duration / 1000);
      
      // 基准值
      this.benchmarks.imageRenderingTime = 2000; // 2秒内完成
      this.benchmarks.imageRenderingThroughput = 25; // 每秒至少25张
      
      console.log(`图片渲染时间: ${duration.toFixed(2)}ms`);
      console.log(`图片渲染吞吐量: ${this.metrics.imageRenderingThroughput.toFixed(2)} 图片/秒`);
      
    } catch (error) {
      console.error('图片渲染性能测试失败:', error);
    }
  }

  async testWorkerPerformance() {
    console.log('测试Worker性能...');
    
    const startTime = performance.now();
    
    try {
      const workerManager = this.app.workerManager;
      
      // 并发提交多个任务
      const tasks = [];
      for (let i = 0; i < 20; i++) {
        tasks.push(
          workerManager.submitTask('imageProcessor', 'extractMetadata', {
            imageUrl: `https://picsum.photos/300/200?random=worker_${i}`
          })
        );
      }
      
      const results = await Promise.all(tasks);
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      this.metrics.workerProcessingTime = duration;
      this.metrics.workerThroughput = tasks.length / (duration / 1000);
      
      // 基准值
      this.benchmarks.workerProcessingTime = 3000; // 3秒内完成
      this.benchmarks.workerThroughput = 6; // 每秒至少6个任务
      
      console.log(`Worker处理时间: ${duration.toFixed(2)}ms`);
      console.log(`Worker吞吐量: ${this.metrics.workerThroughput.toFixed(2)} 任务/秒`);
      
    } catch (error) {
      console.error('Worker性能测试失败:', error);
    }
  }

  async testAnimationPerformance() {
    console.log('测试动画性能...');
    
    const startTime = performance.now();
    
    try {
      const animationController = this.app.animationController;
      
      // 创建多个测试元素
      const testElements = [];
      for (let i = 0; i < 20; i++) {
        const element = document.createElement('div');
        element.style.cssText = `
          position: absolute;
          width: 50px;
          height: 50px;
          background: blue;
          opacity: 0;
          left: ${i * 60}px;
          top: 100px;
        `;
        document.body.appendChild(element);
        testElements.push(element);
      }
      
      // 并发执行动画
      const animations = testElements.map(element => 
        animationController.animate(element, 'fadeIn', { duration: 500 })
      );
      
      await Promise.all(animations.map(anim => 
        new Promise(resolve => {
          if (anim && anim.complete) {
            anim.complete = resolve;
          } else {
            setTimeout(resolve, 600);
          }
        })
      ));
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      this.metrics.animationTime = duration;
      this.metrics.animationThroughput = testElements.length / (duration / 1000);
      
      // 清理测试元素
      testElements.forEach(element => element.remove());
      
      // 基准值
      this.benchmarks.animationTime = 1000; // 1秒内完成
      this.benchmarks.animationThroughput = 20; // 每秒至少20个动画
      
      console.log(`动画执行时间: ${duration.toFixed(2)}ms`);
      console.log(`动画吞吐量: ${this.metrics.animationThroughput.toFixed(2)} 动画/秒`);
      
    } catch (error) {
      console.error('动画性能测试失败:', error);
    }
  }

  async testMemoryUsage() {
    console.log('测试内存使用...');
    
    if (!performance.memory) {
      console.warn('浏览器不支持内存监控');
      return;
    }
    
    const beforeMemory = performance.memory.usedJSHeapSize;
    
    // 执行一些内存密集型操作
    const largeArray = new Array(100000).fill(0).map((_, i) => ({
      id: i,
      data: new Array(100).fill(Math.random())
    }));
    
    // 强制垃圾回收（如果支持）
    if (window.gc) {
      window.gc();
    }
    
    const afterMemory = performance.memory.usedJSHeapSize;
    const memoryIncrease = afterMemory - beforeMemory;
    
    this.metrics.memoryUsage = {
      before: beforeMemory,
      after: afterMemory,
      increase: memoryIncrease,
      total: performance.memory.totalJSHeapSize,
      limit: performance.memory.jsHeapSizeLimit
    };
    
    // 基准值（字节）
    this.benchmarks.memoryIncrease = 50 * 1024 * 1024; // 50MB以内
    
    console.log(`内存使用增加: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
    console.log(`总内存使用: ${(afterMemory / 1024 / 1024).toFixed(2)}MB`);
    
    // 清理
    largeArray.length = 0;
  }

  generatePerformanceReport() {
    console.log('\n📊 性能测试报告:');
    console.log('='.repeat(60));
    
    const report = {
      summary: {
        totalTests: 0,
        passedTests: 0,
        failedTests: 0
      },
      details: []
    };
    
    // 检查各项指标
    Object.keys(this.metrics).forEach(key => {
      if (this.benchmarks[key] !== undefined) {
        const metric = this.metrics[key];
        const benchmark = this.benchmarks[key];
        const passed = metric <= benchmark;
        
        report.summary.totalTests++;
        if (passed) {
          report.summary.passedTests++;
        } else {
          report.summary.failedTests++;
        }
        
        const status = passed ? '✅ PASS' : '❌ FAIL';
        const percentage = ((metric / benchmark) * 100).toFixed(1);
        
        console.log(`${status} ${key}: ${metric.toFixed ? metric.toFixed(2) : metric} (基准: ${benchmark}, ${percentage}%)`);
        
        report.details.push({
          name: key,
          value: metric,
          benchmark: benchmark,
          passed: passed,
          percentage: percentage
        });
      }
    });
    
    // 特殊处理内存使用
    if (this.metrics.memoryUsage) {
      const memIncrease = this.metrics.memoryUsage.increase;
      const memBenchmark = this.benchmarks.memoryIncrease;
      const memPassed = memIncrease <= memBenchmark;
      
      report.summary.totalTests++;
      if (memPassed) {
        report.summary.passedTests++;
      } else {
        report.summary.failedTests++;
      }
      
      const memStatus = memPassed ? '✅ PASS' : '❌ FAIL';
      const memMB = (memIncrease / 1024 / 1024).toFixed(2);
      const benchmarkMB = (memBenchmark / 1024 / 1024).toFixed(2);
      
      console.log(`${memStatus} memoryIncrease: ${memMB}MB (基准: ${benchmarkMB}MB)`);
    }
    
    console.log('='.repeat(60));
    console.log(`性能测试汇总: ${report.summary.passedTests}/${report.summary.totalTests} 通过`);
    console.log(`性能评分: ${((report.summary.passedTests / report.summary.totalTests) * 100).toFixed(1)}%`);
    
    // 在页面上显示报告
    this.displayReportOnPage(report);
    
    return report;
  }

  displayReportOnPage(report) {
    const reportContainer = document.createElement('div');
    reportContainer.id = 'performance-report';
    reportContainer.className = 'fixed top-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 z-50 max-w-md max-h-96 overflow-y-auto';
    
    const score = ((report.summary.passedTests / report.summary.totalTests) * 100).toFixed(1);
    const scoreColor = score >= 80 ? 'text-green-600' : score >= 60 ? 'text-yellow-600' : 'text-red-600';
    
    reportContainer.innerHTML = `
      <div class="flex justify-between items-center mb-3">
        <h3 class="text-lg font-semibold">性能报告</h3>
        <button onclick="this.parentElement.parentElement.remove()" class="text-gray-500 hover:text-gray-700">×</button>
      </div>
      <div class="mb-3">
        <div class="text-2xl font-bold ${scoreColor}">${score}%</div>
        <div class="text-sm text-gray-600">
          ${report.summary.passedTests}/${report.summary.totalTests} 项通过
        </div>
      </div>
      <div class="space-y-2">
        ${report.details.map(detail => `
          <div class="flex justify-between items-center text-sm">
            <span class="font-medium">${detail.name}</span>
            <span class="${detail.passed ? 'text-green-500' : 'text-red-500'}">${detail.percentage}%</span>
          </div>
        `).join('')}
      </div>
    `;
    
    document.body.appendChild(reportContainer);
  }
}

// 自动运行性能测试（如果在测试环境中）
if (typeof window !== 'undefined' && window.location.search.includes('perf=true')) {
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
      const test = new PerformanceTest();
      test.runPerformanceTests();
    }, 3000); // 等待应用完全加载
  });
}

// 导出测试类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PerformanceTest;
}
