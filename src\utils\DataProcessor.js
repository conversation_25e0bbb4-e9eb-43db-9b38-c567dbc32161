export class DataProcessor {
  constructor(workerManager) {
    this.workerManager = workerManager;
    this.processingQueue = [];
    this.results = new Map();
    this.isProcessing = false;
  }

  // 批量处理图片数据
  async batchProcessImages(images, options = {}) {
    console.log(`🔄 开始批量处理 ${images.length} 张图片...`);
    
    const defaultOptions = {
      generateThumbnails: true,
      extractColors: true,
      analyzeQuality: true,
      compressImages: false,
      batchSize: 5,
      ...options
    };

    const results = [];
    const errors = [];
    
    // 分批处理以避免过载
    for (let i = 0; i < images.length; i += defaultOptions.batchSize) {
      const batch = images.slice(i, i + defaultOptions.batchSize);
      
      try {
        const batchResults = await this.processBatch(batch, defaultOptions);
        results.push(...batchResults);
        
        // 更新进度
        const progress = Math.round(((i + batch.length) / images.length) * 100);
        this.notifyProgress('图片处理', progress, `已处理 ${i + batch.length}/${images.length} 张图片`);
        
      } catch (error) {
        console.error(`批次 ${Math.floor(i / defaultOptions.batchSize) + 1} 处理失败:`, error);
        errors.push({ batch: Math.floor(i / defaultOptions.batchSize) + 1, error });
      }
    }

    console.log(`✅ 图片批量处理完成: ${results.length} 成功, ${errors.length} 失败`);
    
    return {
      results,
      errors,
      totalProcessed: results.length,
      totalErrors: errors.length
    };
  }

  async processBatch(images, options) {
    const promises = images.map(async (image) => {
      const imageResult = { ...image, processing: {} };
      
      try {
        // 并行执行多个处理任务
        const tasks = [];
        
        if (options.generateThumbnails) {
          tasks.push(
            this.workerManager.generateThumbnail(image.url, 300)
              .then(result => imageResult.processing.thumbnails = result)
          );
        }
        
        if (options.extractColors) {
          tasks.push(
            this.workerManager.submitTask('imageProcessor', 'analyzeColors', {
              imageUrl: image.url,
              colorCount: 5
            }).then(result => imageResult.processing.colors = result)
          );
        }
        
        if (options.analyzeQuality) {
          tasks.push(
            this.workerManager.extractMetadata(image.url)
              .then(result => imageResult.processing.metadata = result)
          );
        }
        
        if (options.compressImages) {
          tasks.push(
            this.workerManager.submitTask('imageProcessor', 'compressImage', {
              imageUrl: image.url,
              quality: 80
            }).then(result => imageResult.processing.compressed = result)
          );
        }
        
        // 等待所有任务完成
        await Promise.allSettled(tasks);
        
        imageResult.processed = true;
        imageResult.processedAt = new Date().toISOString();
        
        return imageResult;
        
      } catch (error) {
        console.error(`处理图片失败: ${image.url}`, error);
        imageResult.error = error.message;
        return imageResult;
      }
    });
    
    return Promise.all(promises);
  }

  // 高级数据分析
  async analyzeImageCollection(images) {
    console.log(`📊 开始分析图片集合 (${images.length} 张图片)...`);
    
    try {
      // 使用Worker进行数据分析
      const analysisResult = await this.workerManager.analyzeData(images);
      
      // 额外的高级分析
      const advancedAnalysis = await this.performAdvancedAnalysis(images, analysisResult);
      
      return {
        ...analysisResult,
        advanced: advancedAnalysis,
        analyzedAt: new Date().toISOString()
      };
      
    } catch (error) {
      console.error('图片集合分析失败:', error);
      throw error;
    }
  }

  async performAdvancedAnalysis(images, basicAnalysis) {
    const analysis = {
      trends: {},
      recommendations: [],
      insights: [],
      performance: {}
    };

    // 分析趋势
    analysis.trends = await this.analyzeTrends(images);
    
    // 生成推荐
    analysis.recommendations = this.generateRecommendations(basicAnalysis);
    
    // 提取洞察
    analysis.insights = this.extractInsights(images, basicAnalysis);
    
    // 性能分析
    analysis.performance = this.analyzePerformance(images);
    
    return analysis;
  }

  async analyzeTrends(images) {
    // 时间趋势分析
    const timeGroups = {};
    images.forEach(image => {
      const date = new Date(image.createdAt);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      timeGroups[monthKey] = (timeGroups[monthKey] || 0) + 1;
    });

    // 尺寸趋势
    const sizeRanges = {
      small: images.filter(img => img.width * img.height < 500000).length,
      medium: images.filter(img => img.width * img.height >= 500000 && img.width * img.height < 2000000).length,
      large: images.filter(img => img.width * img.height >= 2000000).length
    };

    // 质量趋势
    const qualityDistribution = {
      high: images.filter(img => img.quality && img.quality.score >= 80).length,
      medium: images.filter(img => img.quality && img.quality.score >= 60 && img.quality.score < 80).length,
      low: images.filter(img => img.quality && img.quality.score < 60).length
    };

    return {
      temporal: timeGroups,
      sizeDistribution: sizeRanges,
      qualityDistribution: qualityDistribution,
      growthRate: this.calculateGrowthRate(timeGroups)
    };
  }

  generateRecommendations(analysis) {
    const recommendations = [];

    // 基于分类分布的推荐
    const categories = Object.entries(analysis.categories);
    if (categories.length > 0) {
      const topCategory = categories.reduce((a, b) => a[1] > b[1] ? a : b);
      recommendations.push({
        type: 'content',
        priority: 'high',
        message: `${topCategory[0]} 类别图片最多 (${topCategory[1]} 张)，建议继续收集此类内容`
      });
    }

    // 基于质量的推荐
    if (analysis.qualityStats && analysis.qualityStats.averageScore < 70) {
      recommendations.push({
        type: 'quality',
        priority: 'medium',
        message: '图片平均质量偏低，建议提高采集标准或进行后期处理'
      });
    }

    // 基于来源的推荐
    const sources = Object.entries(analysis.sources || {});
    if (sources.length === 1) {
      recommendations.push({
        type: 'diversity',
        priority: 'medium',
        message: '图片来源单一，建议增加更多数据源以提高多样性'
      });
    }

    return recommendations;
  }

  extractInsights(images, analysis) {
    const insights = [];

    // 数据量洞察
    if (images.length > 1000) {
      insights.push({
        type: 'volume',
        message: `数据集规模较大 (${images.length} 张)，适合进行机器学习训练`,
        confidence: 0.9
      });
    }

    // 多样性洞察
    const uniqueCategories = Object.keys(analysis.categories || {}).length;
    if (uniqueCategories > 10) {
      insights.push({
        type: 'diversity',
        message: `类别丰富度高 (${uniqueCategories} 个类别)，数据集多样性良好`,
        confidence: 0.8
      });
    }

    // 质量洞察
    if (analysis.qualityStats && analysis.qualityStats.highQuality > analysis.qualityStats.lowQuality * 2) {
      insights.push({
        type: 'quality',
        message: '高质量图片占比较高，数据集质量优秀',
        confidence: 0.85
      });
    }

    return insights;
  }

  analyzePerformance(images) {
    const totalSize = images.reduce((sum, img) => {
      return sum + (img.metadata?.fileSize || 0);
    }, 0);

    const avgSize = totalSize / images.length;
    const avgDimensions = {
      width: images.reduce((sum, img) => sum + (img.width || 0), 0) / images.length,
      height: images.reduce((sum, img) => sum + (img.height || 0), 0) / images.length
    };

    return {
      totalSize: totalSize,
      averageSize: avgSize,
      averageDimensions: avgDimensions,
      compressionPotential: this.calculateCompressionPotential(images),
      loadingEstimate: this.estimateLoadingTime(totalSize)
    };
  }

  calculateGrowthRate(timeGroups) {
    const months = Object.keys(timeGroups).sort();
    if (months.length < 2) return 0;

    const recent = timeGroups[months[months.length - 1]];
    const previous = timeGroups[months[months.length - 2]];
    
    return previous > 0 ? ((recent - previous) / previous * 100).toFixed(1) : 0;
  }

  calculateCompressionPotential(images) {
    // 估算压缩潜力
    const uncompressedImages = images.filter(img => 
      !img.processing?.compressed && 
      img.metadata?.format === 'PNG'
    );
    
    return {
      candidates: uncompressedImages.length,
      estimatedSavings: uncompressedImages.length * 0.3 // 估算30%的压缩率
    };
  }

  estimateLoadingTime(totalSize) {
    // 基于不同网络速度估算加载时间
    const speeds = {
      '3G': 1.5 * 1024 * 1024, // 1.5 Mbps
      '4G': 10 * 1024 * 1024,  // 10 Mbps
      'WiFi': 50 * 1024 * 1024 // 50 Mbps
    };

    const estimates = {};
    Object.entries(speeds).forEach(([network, speed]) => {
      estimates[network] = Math.round(totalSize / speed);
    });

    return estimates;
  }

  // 智能过滤和搜索
  async smartFilter(images, criteria) {
    console.log('🔍 执行智能过滤...');
    
    try {
      // 使用Worker进行复杂过滤
      const filterResult = await this.workerManager.filterData(images, criteria);
      
      // 添加智能排序
      if (criteria.smartSort) {
        const sortedResult = await this.applySmart排序(filterResult.items, criteria.smartSort);
        filterResult.items = sortedResult.items;
        filterResult.sortingApplied = sortedResult.criteria;
      }
      
      return filterResult;
      
    } catch (error) {
      console.error('智能过滤失败:', error);
      throw error;
    }
  }

  async applySmartSort(images, sortCriteria) {
    // 智能排序算法
    const scoredImages = images.map(image => ({
      ...image,
      smartScore: this.calculateSmartScore(image, sortCriteria)
    }));

    const sorted = await this.workerManager.sortData(scoredImages, 'smartScore', 'desc');
    
    return {
      items: sorted.items,
      criteria: sortCriteria
    };
  }

  calculateSmartScore(image, criteria) {
    let score = 0;
    
    // 质量权重
    if (image.quality?.score) {
      score += image.quality.score * (criteria.qualityWeight || 0.3);
    }
    
    // 尺寸权重
    if (image.width && image.height) {
      const resolution = image.width * image.height;
      score += Math.min(resolution / 1000000, 10) * (criteria.resolutionWeight || 0.2);
    }
    
    // 新鲜度权重
    if (image.createdAt) {
      const age = Date.now() - new Date(image.createdAt).getTime();
      const daysSinceCreation = age / (1000 * 60 * 60 * 24);
      score += Math.max(0, 30 - daysSinceCreation) * (criteria.freshnessWeight || 0.2);
    }
    
    // 多样性权重
    if (image.tags?.length) {
      score += image.tags.length * (criteria.diversityWeight || 0.1);
    }
    
    return score;
  }

  // 进度通知
  notifyProgress(operation, percentage, details) {
    const event = new CustomEvent('dataProcessingProgress', {
      detail: {
        operation,
        percentage,
        details,
        timestamp: Date.now()
      }
    });
    document.dispatchEvent(event);
  }

  // 获取处理统计
  getProcessingStats() {
    return {
      queueLength: this.processingQueue.length,
      resultsCount: this.results.size,
      isProcessing: this.isProcessing,
      workerStats: this.workerManager.getWorkerStats()
    };
  }
}
