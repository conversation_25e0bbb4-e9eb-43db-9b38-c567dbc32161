import { animate, utils } from 'animejs';

export class GestureRecognizer {
  constructor(element, options = {}) {
    this.element = typeof element === 'string' ? 
      document.querySelector(element) : element;
    
    this.options = {
      swipeThreshold: 50,
      swipeTimeout: 300,
      tapTimeout: 200,
      doubleTapTimeout: 300,
      longPressTimeout: 500,
      pinchThreshold: 10,
      ...options
    };
    
    this.isTouch = false;
    this.startTime = 0;
    this.startX = 0;
    this.startY = 0;
    this.currentX = 0;
    this.currentY = 0;
    this.lastTap = 0;
    this.longPressTimer = null;
    this.isLongPress = false;
    
    // 多点触控
    this.touches = [];
    this.initialDistance = 0;
    this.currentDistance = 0;
    
    // 事件回调
    this.callbacks = {
      tap: [],
      doubleTap: [],
      longPress: [],
      swipeLeft: [],
      swipeRight: [],
      swipeUp: [],
      swipeDown: [],
      pinchIn: [],
      pinchOut: [],
      pan: [],
      panStart: [],
      panEnd: []
    };
    
    this.init();
  }

  init() {
    if (!this.element) {
      console.error('Gesture recognizer element not found');
      return;
    }
    
    // 绑定事件
    this.bindEvents();
  }

  bindEvents() {
    // 触摸事件
    this.element.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false });
    this.element.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false });
    this.element.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: false });
    this.element.addEventListener('touchcancel', this.handleTouchEnd.bind(this), { passive: false });
    
    // 鼠标事件（用于桌面端测试）
    this.element.addEventListener('mousedown', this.handleMouseDown.bind(this));
    this.element.addEventListener('mousemove', this.handleMouseMove.bind(this));
    this.element.addEventListener('mouseup', this.handleMouseUp.bind(this));
    this.element.addEventListener('mouseleave', this.handleMouseUp.bind(this));
    
    // 阻止默认的触摸行为
    this.element.addEventListener('touchstart', (e) => {
      if (this.options.preventDefault) {
        e.preventDefault();
      }
    });
  }

  handleTouchStart(e) {
    this.isTouch = true;
    this.touches = Array.from(e.touches);
    this.startGesture(e.touches[0]);
    
    // 多点触控
    if (e.touches.length === 2) {
      this.initialDistance = this.getDistance(e.touches[0], e.touches[1]);
    }
  }

  handleTouchMove(e) {
    if (!this.isTouch) return;
    
    this.touches = Array.from(e.touches);
    
    if (e.touches.length === 1) {
      this.updateGesture(e.touches[0]);
      this.triggerPan();
    } else if (e.touches.length === 2) {
      this.handlePinch(e.touches[0], e.touches[1]);
    }
  }

  handleTouchEnd(e) {
    if (!this.isTouch) return;
    
    this.endGesture();
    this.isTouch = false;
    this.touches = [];
  }

  handleMouseDown(e) {
    if (this.isTouch) return; // 忽略鼠标事件如果正在触摸
    
    this.startGesture(e);
  }

  handleMouseMove(e) {
    if (this.isTouch || !this.startTime) return;
    
    this.updateGesture(e);
    this.triggerPan();
  }

  handleMouseUp(e) {
    if (this.isTouch) return;
    
    this.endGesture();
  }

  startGesture(point) {
    this.startTime = Date.now();
    this.startX = point.clientX;
    this.startY = point.clientY;
    this.currentX = point.clientX;
    this.currentY = point.clientY;
    this.isLongPress = false;
    
    // 清除之前的长按计时器
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
    }
    
    // 设置长按计时器
    this.longPressTimer = setTimeout(() => {
      this.isLongPress = true;
      this.trigger('longPress', {
        x: this.currentX,
        y: this.currentY,
        startX: this.startX,
        startY: this.startY
      });
    }, this.options.longPressTimeout);
    
    // 触发pan开始
    this.trigger('panStart', {
      x: this.currentX,
      y: this.currentY
    });
  }

  updateGesture(point) {
    this.currentX = point.clientX;
    this.currentY = point.clientY;
  }

  endGesture() {
    const endTime = Date.now();
    const duration = endTime - this.startTime;
    const deltaX = this.currentX - this.startX;
    const deltaY = this.currentY - this.startY;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    
    // 清除长按计时器
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }
    
    // 触发pan结束
    this.trigger('panEnd', {
      x: this.currentX,
      y: this.currentY,
      deltaX: deltaX,
      deltaY: deltaY,
      distance: distance
    });
    
    // 如果是长按，不处理其他手势
    if (this.isLongPress) {
      this.resetGesture();
      return;
    }
    
    // 检测滑动
    if (distance > this.options.swipeThreshold && duration < this.options.swipeTimeout) {
      this.detectSwipe(deltaX, deltaY);
    }
    // 检测点击
    else if (distance < 10 && duration < this.options.tapTimeout) {
      this.detectTap();
    }
    
    this.resetGesture();
  }

  detectSwipe(deltaX, deltaY) {
    const absDeltaX = Math.abs(deltaX);
    const absDeltaY = Math.abs(deltaY);
    
    if (absDeltaX > absDeltaY) {
      // 水平滑动
      if (deltaX > 0) {
        this.trigger('swipeRight', { deltaX, deltaY });
      } else {
        this.trigger('swipeLeft', { deltaX, deltaY });
      }
    } else {
      // 垂直滑动
      if (deltaY > 0) {
        this.trigger('swipeDown', { deltaX, deltaY });
      } else {
        this.trigger('swipeUp', { deltaX, deltaY });
      }
    }
  }

  detectTap() {
    const now = Date.now();
    const timeSinceLastTap = now - this.lastTap;
    
    if (timeSinceLastTap < this.options.doubleTapTimeout) {
      // 双击
      this.trigger('doubleTap', {
        x: this.currentX,
        y: this.currentY
      });
      this.lastTap = 0; // 重置以避免三击
    } else {
      // 单击
      this.trigger('tap', {
        x: this.currentX,
        y: this.currentY
      });
      this.lastTap = now;
    }
  }

  handlePinch(touch1, touch2) {
    this.currentDistance = this.getDistance(touch1, touch2);
    const deltaDistance = this.currentDistance - this.initialDistance;
    
    if (Math.abs(deltaDistance) > this.options.pinchThreshold) {
      if (deltaDistance > 0) {
        this.trigger('pinchOut', {
          scale: this.currentDistance / this.initialDistance,
          distance: this.currentDistance,
          delta: deltaDistance
        });
      } else {
        this.trigger('pinchIn', {
          scale: this.currentDistance / this.initialDistance,
          distance: this.currentDistance,
          delta: deltaDistance
        });
      }
    }
  }

  triggerPan() {
    const deltaX = this.currentX - this.startX;
    const deltaY = this.currentY - this.startY;
    
    this.trigger('pan', {
      x: this.currentX,
      y: this.currentY,
      deltaX: deltaX,
      deltaY: deltaY,
      distance: Math.sqrt(deltaX * deltaX + deltaY * deltaY)
    });
  }

  getDistance(touch1, touch2) {
    const dx = touch1.clientX - touch2.clientX;
    const dy = touch1.clientY - touch2.clientY;
    return Math.sqrt(dx * dx + dy * dy);
  }

  resetGesture() {
    this.startTime = 0;
    this.startX = 0;
    this.startY = 0;
    this.currentX = 0;
    this.currentY = 0;
    this.initialDistance = 0;
    this.currentDistance = 0;
  }

  // 事件监听器管理
  on(eventType, callback) {
    if (this.callbacks[eventType]) {
      this.callbacks[eventType].push(callback);
    }
    return this;
  }

  off(eventType, callback) {
    if (this.callbacks[eventType]) {
      const index = this.callbacks[eventType].indexOf(callback);
      if (index > -1) {
        this.callbacks[eventType].splice(index, 1);
      }
    }
    return this;
  }

  trigger(eventType, data = {}) {
    if (this.callbacks[eventType]) {
      this.callbacks[eventType].forEach(callback => {
        callback(data);
      });
    }
  }

  // 便捷方法
  onTap(callback) { return this.on('tap', callback); }
  onDoubleTap(callback) { return this.on('doubleTap', callback); }
  onLongPress(callback) { return this.on('longPress', callback); }
  onSwipeLeft(callback) { return this.on('swipeLeft', callback); }
  onSwipeRight(callback) { return this.on('swipeRight', callback); }
  onSwipeUp(callback) { return this.on('swipeUp', callback); }
  onSwipeDown(callback) { return this.on('swipeDown', callback); }
  onPinchIn(callback) { return this.on('pinchIn', callback); }
  onPinchOut(callback) { return this.on('pinchOut', callback); }
  onPan(callback) { return this.on('pan', callback); }
  onPanStart(callback) { return this.on('panStart', callback); }
  onPanEnd(callback) { return this.on('panEnd', callback); }

  // 销毁手势识别器
  destroy() {
    // 移除所有事件监听器
    this.element.removeEventListener('touchstart', this.handleTouchStart);
    this.element.removeEventListener('touchmove', this.handleTouchMove);
    this.element.removeEventListener('touchend', this.handleTouchEnd);
    this.element.removeEventListener('touchcancel', this.handleTouchEnd);
    this.element.removeEventListener('mousedown', this.handleMouseDown);
    this.element.removeEventListener('mousemove', this.handleMouseMove);
    this.element.removeEventListener('mouseup', this.handleMouseUp);
    this.element.removeEventListener('mouseleave', this.handleMouseUp);
    
    // 清除计时器
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
    }
    
    // 清除回调
    Object.keys(this.callbacks).forEach(key => {
      this.callbacks[key] = [];
    });
  }
}
