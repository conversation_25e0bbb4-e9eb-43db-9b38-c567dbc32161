import { animate, utils } from 'animejs';

export class WorkerMonitor {
  constructor(workerManager) {
    this.workerManager = workerManager;
    this.isVisible = false;
    this.updateInterval = null;
    this.performanceData = {
      workers: {},
      tasks: {
        completed: 0,
        failed: 0,
        pending: 0
      },
      performance: {
        avgResponseTime: 0,
        throughput: 0,
        errorRate: 0
      }
    };
    
    this.init();
  }

  init() {
    this.createMonitorPanel();
    this.bindEvents();
    this.startMonitoring();
  }

  createMonitorPanel() {
    this.panel = document.createElement('div');
    this.panel.id = 'worker-monitor';
    this.panel.className = 'fixed bottom-4 right-4 bg-white rounded-lg shadow-xl border border-gray-200 z-40 hidden';
    this.panel.style.width = '400px';
    this.panel.style.maxHeight = '500px';
    
    this.panel.innerHTML = `
      <div class="flex items-center justify-between p-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center space-x-2">
          <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
          </svg>
          <span>Worker 监控</span>
        </h3>
        <div class="flex items-center space-x-2">
          <button id="toggle-auto-refresh" class="text-sm px-2 py-1 bg-gray-100 rounded hover:bg-gray-200 transition-colors">
            自动刷新
          </button>
          <button id="close-monitor" class="text-gray-400 hover:text-gray-600 transition-colors">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>
      
      <div class="p-4 overflow-y-auto" style="max-height: 400px;">
        <!-- 总览统计 -->
        <div class="mb-4">
          <h4 class="text-sm font-medium text-gray-700 mb-2">总览</h4>
          <div class="grid grid-cols-3 gap-3">
            <div class="bg-blue-50 p-3 rounded-lg">
              <div class="text-xs text-blue-600 font-medium">活跃Worker</div>
              <div id="active-workers" class="text-lg font-semibold text-blue-900">0</div>
            </div>
            <div class="bg-green-50 p-3 rounded-lg">
              <div class="text-xs text-green-600 font-medium">已完成任务</div>
              <div id="completed-tasks" class="text-lg font-semibold text-green-900">0</div>
            </div>
            <div class="bg-yellow-50 p-3 rounded-lg">
              <div class="text-xs text-yellow-600 font-medium">队列任务</div>
              <div id="queued-tasks" class="text-lg font-semibold text-yellow-900">0</div>
            </div>
          </div>
        </div>
        
        <!-- 性能指标 -->
        <div class="mb-4">
          <h4 class="text-sm font-medium text-gray-700 mb-2">性能指标</h4>
          <div class="space-y-2">
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-600">平均响应时间</span>
              <span id="avg-response-time" class="text-sm font-medium">0ms</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-600">吞吐量</span>
              <span id="throughput" class="text-sm font-medium">0 tasks/s</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-600">错误率</span>
              <span id="error-rate" class="text-sm font-medium">0%</span>
            </div>
          </div>
        </div>
        
        <!-- Worker详情 -->
        <div class="mb-4">
          <h4 class="text-sm font-medium text-gray-700 mb-2">Worker详情</h4>
          <div id="worker-details" class="space-y-2">
            <!-- Worker详情将在这里动态生成 -->
          </div>
        </div>
        
        <!-- 任务队列 -->
        <div>
          <h4 class="text-sm font-medium text-gray-700 mb-2">任务队列</h4>
          <div id="task-queue" class="space-y-1">
            <!-- 任务队列将在这里动态生成 -->
          </div>
        </div>
      </div>
      
      <div class="p-3 border-t border-gray-200 bg-gray-50 text-xs text-gray-500 text-center">
        最后更新: <span id="last-update">--</span>
      </div>
    `;
    
    document.body.appendChild(this.panel);
  }

  bindEvents() {
    // 关闭按钮
    const closeBtn = this.panel.querySelector('#close-monitor');
    closeBtn.addEventListener('click', () => this.hide());
    
    // 自动刷新切换
    const autoRefreshBtn = this.panel.querySelector('#toggle-auto-refresh');
    autoRefreshBtn.addEventListener('click', () => this.toggleAutoRefresh());
    
    // 点击外部关闭
    document.addEventListener('click', (e) => {
      if (this.isVisible && !this.panel.contains(e.target) && !e.target.closest('[data-worker-monitor-trigger]')) {
        this.hide();
      }
    });
  }

  show() {
    if (this.isVisible) return;
    
    this.isVisible = true;
    this.panel.classList.remove('hidden');
    this.updateData();
    
    // 入场动画
    animate(this.panel, {
      translateY: [20, 0],
      opacity: [0, 1],
      duration: 300,
      ease: 'easeOutQuart'
    });
  }

  hide() {
    if (!this.isVisible) return;
    
    animate(this.panel, {
      translateY: [0, 20],
      opacity: [1, 0],
      duration: 200,
      ease: 'easeInQuad',
      complete: () => {
        this.panel.classList.add('hidden');
        this.isVisible = false;
      }
    });
  }

  toggleAutoRefresh() {
    const btn = this.panel.querySelector('#toggle-auto-refresh');
    
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
      btn.textContent = '自动刷新';
      btn.classList.remove('bg-blue-100', 'text-blue-700');
      btn.classList.add('bg-gray-100');
    } else {
      this.startMonitoring();
      btn.textContent = '停止刷新';
      btn.classList.remove('bg-gray-100');
      btn.classList.add('bg-blue-100', 'text-blue-700');
    }
  }

  startMonitoring() {
    if (this.updateInterval) return;
    
    this.updateInterval = setInterval(() => {
      if (this.isVisible) {
        this.updateData();
      }
    }, 1000);
  }

  stopMonitoring() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }

  updateData() {
    const stats = this.workerManager.getWorkerStats();
    
    // 更新总览统计
    this.panel.querySelector('#active-workers').textContent = stats.totalWorkers;
    this.panel.querySelector('#completed-tasks').textContent = this.getTotalCompletedTasks(stats);
    this.panel.querySelector('#queued-tasks').textContent = stats.queuedTasks;
    
    // 更新性能指标
    this.updatePerformanceMetrics(stats);
    
    // 更新Worker详情
    this.updateWorkerDetails(stats);
    
    // 更新任务队列
    this.updateTaskQueue(stats);
    
    // 更新时间戳
    this.panel.querySelector('#last-update').textContent = new Date().toLocaleTimeString();
  }

  getTotalCompletedTasks(stats) {
    return Object.values(stats.workers).reduce((total, worker) => total + worker.tasks, 0);
  }

  updatePerformanceMetrics(stats) {
    // 计算平均响应时间（模拟）
    const avgResponseTime = Math.floor(Math.random() * 200 + 50);
    this.panel.querySelector('#avg-response-time').textContent = `${avgResponseTime}ms`;
    
    // 计算吞吐量
    const throughput = (this.getTotalCompletedTasks(stats) / (Date.now() / 1000)).toFixed(2);
    this.panel.querySelector('#throughput').textContent = `${throughput} tasks/s`;
    
    // 计算错误率（模拟）
    const errorRate = (Math.random() * 5).toFixed(1);
    this.panel.querySelector('#error-rate').textContent = `${errorRate}%`;
  }

  updateWorkerDetails(stats) {
    const container = this.panel.querySelector('#worker-details');
    container.innerHTML = '';
    
    Object.entries(stats.workers).forEach(([name, worker]) => {
      const workerElement = document.createElement('div');
      workerElement.className = 'bg-gray-50 p-2 rounded border';
      
      const statusColor = worker.busy ? 'text-yellow-600' : 'text-green-600';
      const statusText = worker.busy ? '忙碌' : '空闲';
      
      workerElement.innerHTML = `
        <div class="flex justify-between items-center">
          <div>
            <div class="text-sm font-medium">${name}</div>
            <div class="text-xs text-gray-500">已完成: ${worker.tasks} 任务</div>
          </div>
          <div class="text-right">
            <div class="text-xs ${statusColor} font-medium">${statusText}</div>
            <div class="text-xs text-gray-500">运行时间: ${this.formatUptime(worker.uptime)}</div>
          </div>
        </div>
      `;
      
      container.appendChild(workerElement);
    });
  }

  updateTaskQueue(stats) {
    const container = this.panel.querySelector('#task-queue');
    
    if (stats.queuedTasks === 0) {
      container.innerHTML = '<div class="text-sm text-gray-500 text-center py-2">队列为空</div>';
      return;
    }
    
    // 模拟任务队列显示
    container.innerHTML = '';
    for (let i = 0; i < Math.min(stats.queuedTasks, 5); i++) {
      const taskElement = document.createElement('div');
      taskElement.className = 'text-xs bg-yellow-50 p-2 rounded border-l-2 border-yellow-400';
      taskElement.innerHTML = `
        <div class="flex justify-between">
          <span>任务 #${i + 1}</span>
          <span class="text-yellow-600">等待中</span>
        </div>
      `;
      container.appendChild(taskElement);
    }
    
    if (stats.queuedTasks > 5) {
      const moreElement = document.createElement('div');
      moreElement.className = 'text-xs text-gray-500 text-center py-1';
      moreElement.textContent = `还有 ${stats.queuedTasks - 5} 个任务...`;
      container.appendChild(moreElement);
    }
  }

  formatUptime(uptime) {
    const seconds = Math.floor(uptime / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  // 添加性能警告
  checkPerformanceWarnings(stats) {
    const warnings = [];
    
    // 检查Worker负载
    Object.entries(stats.workers).forEach(([name, worker]) => {
      if (worker.busy && worker.tasks > 100) {
        warnings.push(`Worker ${name} 负载过高`);
      }
    });
    
    // 检查队列长度
    if (stats.queuedTasks > 50) {
      warnings.push('任务队列过长，可能需要增加Worker数量');
    }
    
    // 检查内存使用
    if (performance.memory) {
      const memoryUsage = performance.memory.usedJSHeapSize / performance.memory.totalJSHeapSize;
      if (memoryUsage > 0.8) {
        warnings.push('内存使用率过高');
      }
    }
    
    return warnings;
  }

  // 显示性能警告
  showWarnings(warnings) {
    if (warnings.length === 0) return;
    
    const warningContainer = document.createElement('div');
    warningContainer.className = 'mb-4 p-3 bg-red-50 border border-red-200 rounded-lg';
    warningContainer.innerHTML = `
      <h5 class="text-sm font-medium text-red-800 mb-2">性能警告</h5>
      <ul class="text-xs text-red-700 space-y-1">
        ${warnings.map(warning => `<li>• ${warning}</li>`).join('')}
      </ul>
    `;
    
    const container = this.panel.querySelector('.p-4');
    container.insertBefore(warningContainer, container.firstChild);
    
    // 5秒后自动移除警告
    setTimeout(() => {
      if (warningContainer.parentNode) {
        warningContainer.remove();
      }
    }, 5000);
  }

  // 销毁监控器
  destroy() {
    this.stopMonitoring();
    if (this.panel && this.panel.parentNode) {
      this.panel.remove();
    }
  }
}
