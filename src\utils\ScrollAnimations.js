import { animate, utils } from 'animejs';

export class ScrollAnimations {
  constructor() {
    this.observers = new Map();
    this.scrollListeners = new Set();
    this.isScrolling = false;
    this.scrollTimeout = null;
    this.lastScrollY = window.scrollY;
    this.scrollDirection = 'down';
    
    this.init();
  }

  init() {
    // 监听滚动事件
    window.addEventListener('scroll', this.handleScroll.bind(this), { passive: true });
    
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize.bind(this));
  }

  handleScroll() {
    const currentScrollY = window.scrollY;
    this.scrollDirection = currentScrollY > this.lastScrollY ? 'down' : 'up';
    this.lastScrollY = currentScrollY;
    
    if (!this.isScrolling) {
      this.isScrolling = true;
      document.body.classList.add('is-scrolling');
    }
    
    // 清除之前的超时
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
    }
    
    // 设置滚动结束检测
    this.scrollTimeout = setTimeout(() => {
      this.isScrolling = false;
      document.body.classList.remove('is-scrolling');
    }, 150);
    
    // 触发滚动监听器
    this.scrollListeners.forEach(listener => {
      listener({
        scrollY: currentScrollY,
        direction: this.scrollDirection,
        isScrolling: this.isScrolling
      });
    });
  }

  handleResize() {
    // 重新计算所有观察器的阈值
    this.observers.forEach((config, element) => {
      if (config.observer) {
        config.observer.disconnect();
        this.createObserver(element, config.options, config.callback);
      }
    });
  }

  // 创建滚动触发动画
  onScrollIntoView(elements, options = {}, callback = null) {
    const elementList = typeof elements === 'string' ? 
      document.querySelectorAll(elements) : 
      Array.isArray(elements) ? elements : [elements];

    const defaultOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -10% 0px',
      triggerOnce: true,
      animation: 'fadeInUp',
      duration: 800,
      delay: 0,
      stagger: 100,
      ...options
    };

    elementList.forEach((element, index) => {
      if (!element) return;
      
      const observer = this.createObserver(element, defaultOptions, (entry) => {
        if (entry.isIntersecting) {
          const animationDelay = defaultOptions.delay + (index * defaultOptions.stagger);
          
          setTimeout(() => {
            this.playAnimation(element, defaultOptions.animation, {
              duration: defaultOptions.duration,
              ...defaultOptions.animationOptions
            });
            
            if (callback) callback(element, entry);
          }, animationDelay);
          
          if (defaultOptions.triggerOnce) {
            observer.unobserve(element);
          }
        }
      });
    });
  }

  createObserver(element, options, callback) {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(callback);
    }, {
      threshold: options.threshold,
      rootMargin: options.rootMargin
    });

    observer.observe(element);
    
    // 存储观察器配置
    this.observers.set(element, {
      observer,
      options,
      callback
    });
    
    return observer;
  }

  // 预定义动画
  playAnimation(element, animationType, options = {}) {
    const animations = {
      fadeIn: {
        opacity: [0, 1],
        duration: 600,
        ease: 'easeOutQuad'
      },
      
      fadeInUp: {
        opacity: [0, 1],
        translateY: [30, 0],
        duration: 800,
        ease: 'easeOutQuart'
      },
      
      fadeInDown: {
        opacity: [0, 1],
        translateY: [-30, 0],
        duration: 800,
        ease: 'easeOutQuart'
      },
      
      fadeInLeft: {
        opacity: [0, 1],
        translateX: [-30, 0],
        duration: 800,
        ease: 'easeOutQuart'
      },
      
      fadeInRight: {
        opacity: [0, 1],
        translateX: [30, 0],
        duration: 800,
        ease: 'easeOutQuart'
      },
      
      scaleIn: {
        opacity: [0, 1],
        scale: [0.8, 1],
        duration: 600,
        ease: 'easeOutBack'
      },
      
      slideInUp: {
        translateY: ['100%', 0],
        duration: 800,
        ease: 'easeOutQuart'
      },
      
      rotateIn: {
        opacity: [0, 1],
        rotate: [-180, 0],
        scale: [0.5, 1],
        duration: 800,
        ease: 'easeOutBack'
      },
      
      bounceIn: {
        opacity: [0, 1],
        scale: [0.3, 1.05, 0.9, 1],
        duration: 800,
        ease: 'easeOutBounce'
      },
      
      flipInX: {
        opacity: [0, 1],
        rotateX: [-90, 0],
        duration: 800,
        ease: 'easeOutQuart'
      },
      
      zoomIn: {
        opacity: [0, 1],
        scale: [0, 1],
        duration: 600,
        ease: 'easeOutQuart'
      }
    };

    const animationConfig = animations[animationType];
    if (!animationConfig) {
      console.warn(`Animation type "${animationType}" not found`);
      return;
    }

    // 设置初始状态
    if (animationConfig.opacity && animationConfig.opacity[0] === 0) {
      element.style.opacity = '0';
    }

    // 执行动画
    animate(element, {
      ...animationConfig,
      ...options,
      begin: () => {
        element.style.visibility = 'visible';
        if (options.begin) options.begin();
      }
    });
  }

  // 视差滚动效果
  parallax(elements, options = {}) {
    const elementList = typeof elements === 'string' ? 
      document.querySelectorAll(elements) : 
      Array.isArray(elements) ? elements : [elements];

    const defaultOptions = {
      speed: 0.5,
      direction: 'vertical', // 'vertical', 'horizontal'
      ...options
    };

    const updateParallax = () => {
      const scrollY = window.scrollY;
      
      elementList.forEach(element => {
        if (!element) return;
        
        const rect = element.getBoundingClientRect();
        const elementTop = rect.top + scrollY;
        const elementHeight = rect.height;
        const windowHeight = window.innerHeight;
        
        // 计算元素在视口中的位置
        const elementCenter = elementTop + elementHeight / 2;
        const viewportCenter = scrollY + windowHeight / 2;
        const distance = elementCenter - viewportCenter;
        
        // 应用视差效果
        const parallaxValue = distance * defaultOptions.speed;
        
        if (defaultOptions.direction === 'vertical') {
          element.style.transform = `translateY(${parallaxValue}px)`;
        } else {
          element.style.transform = `translateX(${parallaxValue}px)`;
        }
      });
    };

    // 添加到滚动监听器
    this.addScrollListener(updateParallax);
    
    // 初始更新
    updateParallax();
    
    return () => this.removeScrollListener(updateParallax);
  }

  // 滚动进度条
  createScrollProgress(element, options = {}) {
    const progressElement = typeof element === 'string' ? 
      document.querySelector(element) : element;

    if (!progressElement) return;

    const defaultOptions = {
      direction: 'horizontal', // 'horizontal', 'vertical'
      smooth: true,
      ...options
    };

    const updateProgress = () => {
      const scrollTop = window.scrollY;
      const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
      const progress = Math.min(scrollTop / documentHeight, 1);
      
      if (defaultOptions.direction === 'horizontal') {
        progressElement.style.width = `${progress * 100}%`;
      } else {
        progressElement.style.height = `${progress * 100}%`;
      }
    };

    this.addScrollListener(updateProgress);
    updateProgress();
    
    return () => this.removeScrollListener(updateProgress);
  }

  // 滚动触发计数器动画
  animateCounter(element, options = {}) {
    const counterElement = typeof element === 'string' ? 
      document.querySelector(element) : element;

    if (!counterElement) return;

    const defaultOptions = {
      from: 0,
      to: parseInt(counterElement.textContent) || 100,
      duration: 2000,
      ease: 'easeOutQuart',
      format: (value) => Math.round(value),
      ...options
    };

    this.onScrollIntoView(counterElement, {
      threshold: 0.5,
      triggerOnce: true
    }, () => {
      const obj = { value: defaultOptions.from };
      
      animate(obj, {
        value: defaultOptions.to,
        duration: defaultOptions.duration,
        ease: defaultOptions.ease,
        update: () => {
          counterElement.textContent = defaultOptions.format(obj.value);
        }
      });
    });
  }

  // 添加滚动监听器
  addScrollListener(listener) {
    this.scrollListeners.add(listener);
  }

  // 移除滚动监听器
  removeScrollListener(listener) {
    this.scrollListeners.delete(listener);
  }

  // 平滑滚动到元素
  scrollToElement(element, options = {}) {
    const targetElement = typeof element === 'string' ? 
      document.querySelector(element) : element;

    if (!targetElement) return;

    const defaultOptions = {
      duration: 1000,
      offset: 0,
      ease: 'easeInOutQuart',
      ...options
    };

    const targetPosition = targetElement.getBoundingClientRect().top + 
                          window.scrollY + defaultOptions.offset;

    const startPosition = window.scrollY;
    const distance = targetPosition - startPosition;

    const obj = { value: startPosition };
    
    animate(obj, {
      value: targetPosition,
      duration: defaultOptions.duration,
      ease: defaultOptions.ease,
      update: () => {
        window.scrollTo(0, obj.value);
      },
      complete: options.complete
    });
  }

  // 创建滚动吸附效果
  createSnapScroll(sections, options = {}) {
    const sectionElements = typeof sections === 'string' ? 
      document.querySelectorAll(sections) : sections;

    const defaultOptions = {
      threshold: 0.5,
      duration: 800,
      ...options
    };

    let isSnapping = false;

    const handleScroll = () => {
      if (isSnapping) return;

      const scrollY = window.scrollY;
      const windowHeight = window.innerHeight;
      
      sectionElements.forEach((section, index) => {
        const rect = section.getBoundingClientRect();
        const sectionTop = rect.top + scrollY;
        const sectionHeight = rect.height;
        
        // 检查是否需要吸附
        if (rect.top < windowHeight * defaultOptions.threshold && 
            rect.top > -sectionHeight * defaultOptions.threshold) {
          
          isSnapping = true;
          
          this.scrollToElement(section, {
            duration: defaultOptions.duration,
            complete: () => {
              setTimeout(() => {
                isSnapping = false;
              }, 100);
            }
          });
        }
      });
    };

    this.addScrollListener(handleScroll);
    
    return () => this.removeScrollListener(handleScroll);
  }

  // 销毁所有观察器和监听器
  destroy() {
    this.observers.forEach((config) => {
      config.observer.disconnect();
    });
    this.observers.clear();
    this.scrollListeners.clear();
    
    window.removeEventListener('scroll', this.handleScroll);
    window.removeEventListener('resize', this.handleResize);
  }
}
