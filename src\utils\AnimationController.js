import { animate, utils } from 'animejs';
import { ParticleSystem } from './ParticleSystem.js';
import { ScrollAnimations } from './ScrollAnimations.js';
import { GestureRecognizer } from './GestureRecognizer.js';

export class AnimationController {
  constructor() {
    this.activeAnimations = new Map();
    this.animationQueue = [];
    this.isProcessingQueue = false;

    // 高级动画系统
    this.particleSystem = null;
    this.scrollAnimations = new ScrollAnimations();
    this.gestureRecognizers = new Map();
    
    // 预设动画配置
    this.presets = {
      fadeIn: {
        opacity: [0, 1],
        duration: 500,
        ease: 'easeOutQuad'
      },
      slideUp: {
        translateY: [30, 0],
        opacity: [0, 1],
        duration: 600,
        ease: 'easeOutQuart'
      },
      zoomIn: {
        scale: [0.8, 1],
        opacity: [0, 1],
        duration: 400,
        ease: 'easeOutBack'
      },
      bounce: {
        scale: [1, 1.1, 1],
        duration: 300,
        ease: 'easeOutQuad'
      },
      shake: {
        translateX: [0, -10, 10, -10, 10, 0],
        duration: 500,
        ease: 'easeOutQuad'
      },
      pulse: {
        scale: [1, 1.05, 1],
        duration: 1000,
        ease: 'easeInOutSine',
        loop: true
      },
      float: {
        translateY: [0, -10, 0],
        duration: 2000,
        ease: 'easeInOutSine',
        loop: true
      }
    };

    this.init();
  }

  init() {
    // 监听页面可见性变化，暂停/恢复动画
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.pauseAllAnimations();
      } else {
        this.resumeAllAnimations();
      }
    });

    // 监听窗口失焦/聚焦
    window.addEventListener('blur', () => this.pauseAllAnimations());
    window.addEventListener('focus', () => this.resumeAllAnimations());
  }

  // 执行预设动画
  animate(target, presetName, customOptions = {}) {
    const preset = this.presets[presetName];
    if (!preset) {
      console.error(`未找到动画预设: ${presetName}`);
      return null;
    }

    const options = { ...preset, ...customOptions };
    return this.createAnimation(target, options);
  }

  // 创建自定义动画
  createAnimation(target, options) {
    const animationId = this.generateAnimationId();
    
    const animation = animate(target, {
      ...options,
      begin: () => {
        if (options.begin) options.begin();
        this.onAnimationStart(animationId);
      },
      complete: () => {
        if (options.complete) options.complete();
        this.onAnimationComplete(animationId);
      }
    });

    this.activeAnimations.set(animationId, animation);
    return animation;
  }

  // 序列动画
  sequence(animations) {
    return new Promise((resolve) => {
      let currentIndex = 0;
      
      const runNext = () => {
        if (currentIndex >= animations.length) {
          resolve();
          return;
        }

        const { target, preset, options = {} } = animations[currentIndex];
        const animation = this.animate(target, preset, {
          ...options,
          complete: () => {
            if (options.complete) options.complete();
            currentIndex++;
            runNext();
          }
        });

        if (!animation) {
          currentIndex++;
          runNext();
        }
      };

      runNext();
    });
  }

  // 并行动画
  parallel(animations) {
    const promises = animations.map(({ target, preset, options }) => {
      return new Promise(resolve => {
        this.animate(target, preset, {
          ...options,
          complete: () => {
            if (options.complete) options.complete();
            resolve();
          }
        });
      });
    });

    return Promise.all(promises);
  }

  // 交错动画
  stagger(targets, preset, options = {}) {
    const elements = typeof targets === 'string' ? 
      document.querySelectorAll(targets) : 
      Array.from(targets);

    const staggerDelay = options.staggerDelay || 100;
    const baseDelay = options.delay || 0;

    elements.forEach((element, index) => {
      const delay = baseDelay + (index * staggerDelay);
      this.animate(element, preset, {
        ...options,
        delay
      });
    });
  }

  // 路径动画
  animateAlongPath(target, path, options = {}) {
    const pathElement = typeof path === 'string' ? 
      document.querySelector(path) : path;

    if (!pathElement) {
      console.error('路径元素未找到');
      return null;
    }

    const pathLength = pathElement.getTotalLength();
    
    return this.createAnimation(target, {
      ...options,
      update: (anim) => {
        const progress = anim.progress / 100;
        const point = pathElement.getPointAtLength(progress * pathLength);
        
        target.style.transform = `translate(${point.x}px, ${point.y}px)`;
        
        if (options.update) options.update(anim);
      }
    });
  }

  // 滚动触发动画
  onScroll(target, preset, options = {}) {
    const elements = typeof target === 'string' ? 
      document.querySelectorAll(target) : 
      [target];

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.animate(entry.target, preset, options);
          if (!options.repeat) {
            observer.unobserve(entry.target);
          }
        }
      });
    }, {
      threshold: options.threshold || 0.1,
      rootMargin: options.rootMargin || '0px'
    });

    elements.forEach(element => observer.observe(element));
    
    return observer;
  }

  // 鼠标跟随动画
  followMouse(target, options = {}) {
    const element = typeof target === 'string' ? 
      document.querySelector(target) : target;

    if (!element) return null;

    const { 
      speed = 0.1, 
      offset = { x: 0, y: 0 },
      boundary = null 
    } = options;

    let mouseX = 0, mouseY = 0;
    let currentX = 0, currentY = 0;

    const updatePosition = () => {
      const dx = mouseX - currentX;
      const dy = mouseY - currentY;
      
      currentX += dx * speed;
      currentY += dy * speed;

      let finalX = currentX + offset.x;
      let finalY = currentY + offset.y;

      // 边界限制
      if (boundary) {
        const rect = boundary.getBoundingClientRect();
        finalX = Math.max(rect.left, Math.min(rect.right, finalX));
        finalY = Math.max(rect.top, Math.min(rect.bottom, finalY));
      }

      element.style.transform = `translate(${finalX}px, ${finalY}px)`;
      
      requestAnimationFrame(updatePosition);
    };

    const handleMouseMove = (e) => {
      mouseX = e.clientX;
      mouseY = e.clientY;
    };

    document.addEventListener('mousemove', handleMouseMove);
    updatePosition();

    return {
      destroy: () => {
        document.removeEventListener('mousemove', handleMouseMove);
      }
    };
  }

  // 显示设置面板
  showSettings() {
    // 创建设置面板
    const settingsPanel = this.createSettingsPanel();
    document.body.appendChild(settingsPanel);

    // 入场动画
    this.animate(settingsPanel, 'slideUp', {
      duration: 400
    });
  }

  createSettingsPanel() {
    const panel = document.createElement('div');
    panel.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    panel.innerHTML = `
      <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold">动画设置</h3>
          <button class="close-btn text-gray-500 hover:text-gray-700">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">动画速度</label>
            <input type="range" min="0.5" max="2" step="0.1" value="1" class="w-full">
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">减少动画</label>
            <input type="checkbox" class="rounded">
            <span class="ml-2 text-sm text-gray-600">为性能较低的设备减少动画效果</span>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">动画预设</label>
            <select class="w-full border border-gray-300 rounded-md px-3 py-2">
              <option value="default">默认</option>
              <option value="smooth">平滑</option>
              <option value="bouncy">弹性</option>
              <option value="minimal">简约</option>
            </select>
          </div>
        </div>
        
        <div class="flex justify-end space-x-3 mt-6">
          <button class="cancel-btn px-4 py-2 text-gray-600 hover:text-gray-800">取消</button>
          <button class="save-btn px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700">保存</button>
        </div>
      </div>
    `;

    // 绑定事件
    const closeBtn = panel.querySelector('.close-btn');
    const cancelBtn = panel.querySelector('.cancel-btn');
    const saveBtn = panel.querySelector('.save-btn');

    const closePanel = () => {
      this.animate(panel, 'fadeIn', {
        opacity: [1, 0],
        duration: 300,
        complete: () => panel.remove()
      });
    };

    closeBtn.addEventListener('click', closePanel);
    cancelBtn.addEventListener('click', closePanel);
    saveBtn.addEventListener('click', () => {
      // 保存设置逻辑
      console.log('设置已保存');
      closePanel();
    });

    // 点击背景关闭
    panel.addEventListener('click', (e) => {
      if (e.target === panel) closePanel();
    });

    return panel;
  }

  // 暂停所有动画
  pauseAllAnimations() {
    this.activeAnimations.forEach(animation => {
      if (animation.pause) animation.pause();
    });
  }

  // 恢复所有动画
  resumeAllAnimations() {
    this.activeAnimations.forEach(animation => {
      if (animation.play) animation.play();
    });
  }

  // 停止所有动画
  stopAllAnimations() {
    this.activeAnimations.forEach(animation => {
      if (animation.pause) animation.pause();
    });
    this.activeAnimations.clear();
  }

  // 生成动画ID
  generateAnimationId() {
    return `anim_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 动画开始回调
  onAnimationStart(animationId) {
    console.log(`动画开始: ${animationId}`);
  }

  // 动画完成回调
  onAnimationComplete(animationId) {
    console.log(`动画完成: ${animationId}`);
    this.activeAnimations.delete(animationId);
  }

  // 获取活动动画数量
  getActiveAnimationCount() {
    return this.activeAnimations.size;
  }

  // 创建粒子系统
  createParticleSystem(container, options = {}) {
    this.particleSystem = new ParticleSystem(container, options);
    return this.particleSystem;
  }

  // 获取粒子系统
  getParticleSystem() {
    return this.particleSystem;
  }

  // 创建手势识别器
  createGestureRecognizer(element, options = {}) {
    const recognizer = new GestureRecognizer(element, options);
    this.gestureRecognizers.set(element, recognizer);
    return recognizer;
  }

  // 获取手势识别器
  getGestureRecognizer(element) {
    return this.gestureRecognizers.get(element);
  }

  // 创建滚动动画
  createScrollAnimation(elements, options = {}) {
    return this.scrollAnimations.onScrollIntoView(elements, options);
  }

  // 创建视差效果
  createParallax(elements, options = {}) {
    return this.scrollAnimations.parallax(elements, options);
  }

  // 创建滚动进度条
  createScrollProgress(element, options = {}) {
    return this.scrollAnimations.createScrollProgress(element, options);
  }

  // 创建计数器动画
  createCounterAnimation(element, options = {}) {
    return this.scrollAnimations.animateCounter(element, options);
  }

  // 平滑滚动
  smoothScrollTo(element, options = {}) {
    return this.scrollAnimations.scrollToElement(element, options);
  }

  // 创建吸附滚动
  createSnapScroll(sections, options = {}) {
    return this.scrollAnimations.createSnapScroll(sections, options);
  }

  // 高级动画组合
  createImageGalleryAnimations() {
    // 为图片库创建专门的动画效果
    const galleryContainer = document.getElementById('image-gallery');
    if (!galleryContainer) return;

    // 图片加载动画
    this.createScrollAnimation('.result-image, [data-image-id]', {
      animation: 'scaleIn',
      stagger: 50,
      threshold: 0.1
    });

    // 创建手势识别
    const gestureRecognizer = this.createGestureRecognizer(galleryContainer, {
      swipeThreshold: 100
    });

    // 滑动切换视图模式
    gestureRecognizer
      .onSwipeLeft(() => {
        this.triggerViewModeChange('next');
      })
      .onSwipeRight(() => {
        this.triggerViewModeChange('prev');
      })
      .onPinchOut(() => {
        this.triggerZoomIn();
      })
      .onPinchIn(() => {
        this.triggerZoomOut();
      });

    return gestureRecognizer;
  }

  triggerViewModeChange(direction) {
    const viewModeSelect = document.getElementById('view-mode');
    if (!viewModeSelect) return;

    const modes = ['grid', 'list', 'masonry'];
    const currentIndex = modes.indexOf(viewModeSelect.value);
    let newIndex;

    if (direction === 'next') {
      newIndex = (currentIndex + 1) % modes.length;
    } else {
      newIndex = (currentIndex - 1 + modes.length) % modes.length;
    }

    viewModeSelect.value = modes[newIndex];
    viewModeSelect.dispatchEvent(new Event('change'));

    // 显示提示
    this.showViewModeToast(modes[newIndex]);
  }

  showViewModeToast(mode) {
    const modeNames = {
      grid: '网格视图',
      list: '列表视图',
      masonry: '瀑布流'
    };

    const toast = document.createElement('div');
    toast.className = 'fixed top-20 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-75 text-white px-4 py-2 rounded-lg z-50';
    toast.textContent = `切换到${modeNames[mode]}`;

    document.body.appendChild(toast);

    // 入场和退场动画
    animate(toast, {
      opacity: [0, 1],
      translateY: [-20, 0],
      duration: 300,
      ease: 'easeOutQuad',
      complete: () => {
        setTimeout(() => {
          animate(toast, {
            opacity: [1, 0],
            translateY: [0, -20],
            duration: 300,
            ease: 'easeInQuad',
            complete: () => toast.remove()
          });
        }, 1500);
      }
    });
  }

  triggerZoomIn() {
    const gallery = document.getElementById('image-gallery');
    if (!gallery) return;

    gallery.style.transform = 'scale(1.1)';
    gallery.style.transition = 'transform 0.3s ease';

    setTimeout(() => {
      gallery.style.transform = 'scale(1)';
    }, 300);
  }

  triggerZoomOut() {
    const gallery = document.getElementById('image-gallery');
    if (!gallery) return;

    gallery.style.transform = 'scale(0.9)';
    gallery.style.transition = 'transform 0.3s ease';

    setTimeout(() => {
      gallery.style.transform = 'scale(1)';
    }, 300);
  }

  // 创建成功动画效果
  showSuccessAnimation(message, position = { x: 0, y: 0 }) {
    // 创建粒子爆发效果
    if (this.particleSystem) {
      this.particleSystem.burst(position.x, position.y, 15);
      this.particleSystem.textEffect(message, position.x, position.y);
    }

    // 创建成功提示
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 flex items-center space-x-2';
    notification.innerHTML = `
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
      </svg>
      <span>${message}</span>
    `;

    document.body.appendChild(notification);

    // 动画效果
    animate(notification, {
      translateX: [300, 0],
      opacity: [0, 1],
      duration: 500,
      ease: 'easeOutBack',
      complete: () => {
        setTimeout(() => {
          animate(notification, {
            translateX: [0, 300],
            opacity: [1, 0],
            duration: 300,
            ease: 'easeInQuad',
            complete: () => notification.remove()
          });
        }, 3000);
      }
    });
  }

  // 性能监控
  getPerformanceStats() {
    return {
      activeAnimations: this.activeAnimations.size,
      queuedAnimations: this.animationQueue.length,
      particleCount: this.particleSystem ? this.particleSystem.particles.length : 0,
      gestureRecognizers: this.gestureRecognizers.size,
      memoryUsage: performance.memory ? {
        used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024)
      } : null
    };
  }

  // 销毁动画控制器
  destroy() {
    // 停止所有动画
    this.stopAllAnimations();

    // 销毁粒子系统
    if (this.particleSystem) {
      this.particleSystem.destroy();
    }

    // 销毁滚动动画
    if (this.scrollAnimations) {
      this.scrollAnimations.destroy();
    }

    // 销毁手势识别器
    this.gestureRecognizers.forEach(recognizer => {
      recognizer.destroy();
    });
    this.gestureRecognizers.clear();
  }
}
