export class WorkerManager {
  constructor() {
    this.workers = new Map();
    this.workerPool = [];
    this.taskQueue = [];
    this.maxWorkers = navigator.hardwareConcurrency || 4;
    this.isProcessing = false;
    
    this.init();
  }

  init() {
    console.log(`🔧 初始化Worker管理器，最大Worker数: ${this.maxWorkers}`);
    
    // 预创建一些Worker
    this.createWorkerPool();
  }

  createWorkerPool() {
    try {
      // 创建图片处理Worker
      this.createWorker('imageProcessor', '/workers/imageProcessor.worker.js');

      // 创建数据处理Worker
      this.createWorker('dataProcessor', '/workers/dataProcessor.worker.js');

      console.log(`✅ Worker池创建完成，当前Worker数: ${this.workers.size}`);
    } catch (error) {
      console.warn('⚠️ Worker池创建部分失败，应用将继续运行:', error);
    }
  }

  createWorker(name, scriptPath) {
    try {
      // 直接使用Worker文件路径
      const worker = new Worker(scriptPath);
      
      worker.onmessage = (event) => this.handleWorkerMessage(name, event);
      worker.onerror = (error) => this.handleWorkerError(name, error);
      
      this.workers.set(name, {
        worker,
        name,
        busy: false,
        tasks: 0,
        created: Date.now()
      });
      
      console.log(`✅ Worker创建成功: ${name}`);
      
    } catch (error) {
      console.error(`❌ Worker创建失败: ${name}`, error);
    }
  }

  generateWorkerScript(workerType) {
    switch (workerType) {
      case 'imageProcessor':
        return this.getImageProcessorScript();
      case 'dataProcessor':
        return this.getDataProcessorScript();
      default:
        return this.getGenericWorkerScript();
    }
  }

  getImageProcessorScript() {
    return `
      // 图片处理Worker
      self.onmessage = function(event) {
        const { taskId, type, data } = event.data;
        
        try {
          switch (type) {
            case 'processImage':
              processImage(taskId, data);
              break;
            case 'generateThumbnail':
              generateThumbnail(taskId, data);
              break;
            case 'extractMetadata':
              extractMetadata(taskId, data);
              break;
            default:
              throw new Error('未知的图片处理任务类型: ' + type);
          }
        } catch (error) {
          self.postMessage({
            taskId,
            type: 'error',
            error: error.message
          });
        }
      };

      function processImage(taskId, data) {
        // 模拟图片处理
        const { imageUrl, filters } = data;
        
        // 模拟处理时间
        const processingTime = 500 + Math.random() * 1000;
        
        setTimeout(() => {
          self.postMessage({
            taskId,
            type: 'processImage',
            result: {
              processedUrl: imageUrl + '?processed=true',
              filters: filters,
              processingTime: processingTime
            }
          });
        }, processingTime);
      }

      function generateThumbnail(taskId, data) {
        const { imageUrl, size } = data;
        
        // 模拟缩略图生成
        setTimeout(() => {
          self.postMessage({
            taskId,
            type: 'generateThumbnail',
            result: {
              thumbnailUrl: imageUrl.replace(/\\d+x\\d+/, size + 'x' + size),
              originalUrl: imageUrl,
              size: size
            }
          });
        }, 200 + Math.random() * 300);
      }

      function extractMetadata(taskId, data) {
        const { imageUrl } = data;
        
        // 模拟元数据提取
        setTimeout(() => {
          self.postMessage({
            taskId,
            type: 'extractMetadata',
            result: {
              url: imageUrl,
              metadata: {
                width: 800 + Math.floor(Math.random() * 400),
                height: 600 + Math.floor(Math.random() * 400),
                format: 'JPEG',
                fileSize: Math.floor(Math.random() * 2000000) + 500000,
                colorSpace: 'sRGB',
                hasAlpha: false,
                created: new Date().toISOString()
              }
            }
          });
        }, 100 + Math.random() * 200);
      }
    `;
  }

  getDataProcessorScript() {
    return `
      // 数据处理Worker
      self.onmessage = function(event) {
        const { taskId, type, data } = event.data;
        
        try {
          switch (type) {
            case 'processApiData':
              processApiData(taskId, data);
              break;
            case 'filterData':
              filterData(taskId, data);
              break;
            case 'sortData':
              sortData(taskId, data);
              break;
            case 'analyzeData':
              analyzeData(taskId, data);
              break;
            default:
              throw new Error('未知的数据处理任务类型: ' + type);
          }
        } catch (error) {
          self.postMessage({
            taskId,
            type: 'error',
            error: error.message
          });
        }
      };

      function processApiData(taskId, data) {
        const { rawData, source } = data;
        
        // 模拟API数据处理
        setTimeout(() => {
          const processedData = rawData.map(item => ({
            ...item,
            processed: true,
            source: source,
            processedAt: new Date().toISOString()
          }));
          
          self.postMessage({
            taskId,
            type: 'processApiData',
            result: {
              data: processedData,
              count: processedData.length,
              source: source
            }
          });
        }, 300 + Math.random() * 500);
      }

      function filterData(taskId, data) {
        const { items, filters } = data;
        
        // 应用过滤器
        let filteredItems = items;
        
        if (filters.category) {
          filteredItems = filteredItems.filter(item => 
            item.category === filters.category
          );
        }
        
        if (filters.tags && filters.tags.length > 0) {
          filteredItems = filteredItems.filter(item =>
            filters.tags.some(tag => item.tags.includes(tag))
          );
        }
        
        if (filters.dateRange) {
          const { start, end } = filters.dateRange;
          filteredItems = filteredItems.filter(item => {
            const itemDate = new Date(item.createdAt);
            return itemDate >= start && itemDate <= end;
          });
        }
        
        self.postMessage({
          taskId,
          type: 'filterData',
          result: {
            items: filteredItems,
            originalCount: items.length,
            filteredCount: filteredItems.length
          }
        });
      }

      function sortData(taskId, data) {
        const { items, sortBy, order } = data;
        
        const sortedItems = [...items].sort((a, b) => {
          let aVal = a[sortBy];
          let bVal = b[sortBy];
          
          if (sortBy === 'createdAt') {
            aVal = new Date(aVal);
            bVal = new Date(bVal);
          }
          
          if (order === 'desc') {
            return bVal > aVal ? 1 : -1;
          } else {
            return aVal > bVal ? 1 : -1;
          }
        });
        
        self.postMessage({
          taskId,
          type: 'sortData',
          result: {
            items: sortedItems,
            sortBy: sortBy,
            order: order
          }
        });
      }

      function analyzeData(taskId, data) {
        const { items } = data;
        
        // 数据分析
        const analysis = {
          totalItems: items.length,
          categories: {},
          tags: {},
          averageSize: 0,
          dateRange: {
            earliest: null,
            latest: null
          }
        };
        
        let totalSize = 0;
        
        items.forEach(item => {
          // 分类统计
          analysis.categories[item.category] = 
            (analysis.categories[item.category] || 0) + 1;
          
          // 标签统计
          if (item.tags) {
            item.tags.forEach(tag => {
              analysis.tags[tag] = (analysis.tags[tag] || 0) + 1;
            });
          }
          
          // 大小统计
          if (item.metadata && item.metadata.fileSize) {
            totalSize += item.metadata.fileSize;
          }
          
          // 日期范围
          const itemDate = new Date(item.createdAt);
          if (!analysis.dateRange.earliest || itemDate < analysis.dateRange.earliest) {
            analysis.dateRange.earliest = itemDate;
          }
          if (!analysis.dateRange.latest || itemDate > analysis.dateRange.latest) {
            analysis.dateRange.latest = itemDate;
          }
        });
        
        analysis.averageSize = totalSize / items.length;
        
        self.postMessage({
          taskId,
          type: 'analyzeData',
          result: analysis
        });
      }
    `;
  }

  getGenericWorkerScript() {
    return `
      // 通用Worker
      self.onmessage = function(event) {
        const { taskId, type, data } = event.data;
        
        // 简单的回显处理
        self.postMessage({
          taskId,
          type: type,
          result: {
            processed: true,
            data: data,
            timestamp: Date.now()
          }
        });
      };
    `;
  }

  handleWorkerMessage(workerName, event) {
    const { taskId, type, result, error } = event.data;
    
    if (error) {
      console.error(`❌ Worker ${workerName} 任务失败:`, error);
      this.handleTaskError(taskId, error);
    } else {
      console.log(`✅ Worker ${workerName} 任务完成:`, type);
      this.handleTaskComplete(taskId, result);
    }
    
    // 标记Worker为空闲
    const workerInfo = this.workers.get(workerName);
    if (workerInfo) {
      workerInfo.busy = false;
      workerInfo.tasks++;
    }
    
    // 处理队列中的下一个任务
    this.processNextTask();
  }

  handleWorkerError(workerName, error) {
    console.error(`❌ Worker ${workerName} 错误:`, error);
    
    // 重新创建Worker
    this.recreateWorker(workerName);
  }

  recreateWorker(workerName) {
    const workerInfo = this.workers.get(workerName);
    if (workerInfo) {
      workerInfo.worker.terminate();
      this.workers.delete(workerName);
      
      // 重新创建
      setTimeout(() => {
        this.createWorker(workerName, workerInfo.scriptPath);
      }, 1000);
    }
  }

  // 提交任务到Worker
  submitTask(workerType, taskType, data) {
    return new Promise((resolve, reject) => {
      const taskId = this.generateTaskId();
      
      const task = {
        id: taskId,
        workerType,
        taskType,
        data,
        resolve,
        reject,
        created: Date.now()
      };
      
      this.taskQueue.push(task);
      this.processNextTask();
    });
  }

  processNextTask() {
    if (this.taskQueue.length === 0) return;
    
    const task = this.taskQueue.shift();
    const worker = this.getAvailableWorker(task.workerType);
    
    if (!worker) {
      // 没有可用Worker，重新放回队列
      this.taskQueue.unshift(task);
      return;
    }
    
    // 标记Worker为忙碌
    worker.busy = true;
    
    // 存储任务回调
    this.pendingTasks = this.pendingTasks || new Map();
    this.pendingTasks.set(task.id, {
      resolve: task.resolve,
      reject: task.reject
    });
    
    // 发送任务到Worker
    worker.worker.postMessage({
      taskId: task.id,
      type: task.taskType,
      data: task.data
    });
  }

  getAvailableWorker(workerType) {
    const workerInfo = this.workers.get(workerType);
    
    if (workerInfo && !workerInfo.busy) {
      return workerInfo;
    }
    
    return null;
  }

  handleTaskComplete(taskId, result) {
    const task = this.pendingTasks?.get(taskId);
    if (task) {
      task.resolve(result);
      this.pendingTasks.delete(taskId);
    }
  }

  handleTaskError(taskId, error) {
    const task = this.pendingTasks?.get(taskId);
    if (task) {
      task.reject(new Error(error));
      this.pendingTasks.delete(taskId);
    }
  }

  generateTaskId() {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 获取Worker状态
  getWorkerStats() {
    const stats = {
      totalWorkers: this.workers.size,
      busyWorkers: 0,
      queuedTasks: this.taskQueue.length,
      workers: {}
    };
    
    this.workers.forEach((workerInfo, name) => {
      if (workerInfo.busy) stats.busyWorkers++;
      
      stats.workers[name] = {
        busy: workerInfo.busy,
        tasks: workerInfo.tasks,
        uptime: Date.now() - workerInfo.created
      };
    });
    
    return stats;
  }

  // 终止所有Worker
  terminateAll() {
    this.workers.forEach((workerInfo) => {
      workerInfo.worker.terminate();
    });
    
    this.workers.clear();
    this.taskQueue = [];
    
    console.log('🛑 所有Worker已终止');
  }

  // 便捷方法：处理图片
  async processImage(imageUrl, filters = {}) {
    return this.submitTask('imageProcessor', 'processImage', {
      imageUrl,
      filters
    });
  }

  // 便捷方法：生成缩略图
  async generateThumbnail(imageUrl, size = 200) {
    return this.submitTask('imageProcessor', 'generateThumbnail', {
      imageUrl,
      size
    });
  }

  // 便捷方法：提取元数据
  async extractMetadata(imageUrl) {
    return this.submitTask('imageProcessor', 'extractMetadata', {
      imageUrl
    });
  }

  // 便捷方法：处理API数据
  async processApiData(rawData, source) {
    return this.submitTask('dataProcessor', 'processApiData', {
      rawData,
      source
    });
  }

  // 便捷方法：过滤数据
  async filterData(items, filters) {
    return this.submitTask('dataProcessor', 'filterData', {
      items,
      filters
    });
  }

  // 便捷方法：排序数据
  async sortData(items, sortBy, order = 'asc') {
    return this.submitTask('dataProcessor', 'sortData', {
      items,
      sortBy,
      order
    });
  }

  // 便捷方法：分析数据
  async analyzeData(items) {
    return this.submitTask('dataProcessor', 'analyzeData', {
      items
    });
  }
}
