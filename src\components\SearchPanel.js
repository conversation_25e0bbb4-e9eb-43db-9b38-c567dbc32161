import { animate, utils } from 'animejs';

export class SearchPanel {
  constructor(dataCollector) {
    this.dataCollector = dataCollector;
    this.isVisible = false;
    this.searchHistory = [];
    this.currentQuery = '';
    
    this.init();
  }

  init() {
    this.createSearchPanel();
    this.bindEvents();
  }

  createSearchPanel() {
    // 创建搜索面板容器
    this.panel = document.createElement('div');
    this.panel.id = 'search-panel';
    this.panel.className = 'fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 z-40 hidden';
    
    this.panel.innerHTML = `
      <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
          <!-- Header -->
          <div class="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">图片搜索</h2>
            <button id="close-search" class="text-gray-400 hover:text-gray-600 transition-colors">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          
          <!-- Search Form -->
          <div class="p-6 border-b border-gray-200">
            <form id="search-form" class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">搜索关键词</label>
                <input 
                  type="text" 
                  id="search-input" 
                  placeholder="输入搜索关键词..."
                  class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  autocomplete="off"
                />
              </div>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">数据源</label>
                  <select id="search-source" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500">
                    <option value="all">所有来源</option>
                    <option value="unsplash">Unsplash</option>
                    <option value="pixabay">Pixabay</option>
                    <option value="pexels">Pexels</option>
                  </select>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">图片数量</label>
                  <select id="search-count" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500">
                    <option value="10">10张</option>
                    <option value="20" selected>20张</option>
                    <option value="30">30张</option>
                    <option value="50">50张</option>
                  </select>
                </div>
              </div>
              
              <div class="flex items-center justify-between">
                <button 
                  type="submit" 
                  id="search-btn"
                  class="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors flex items-center space-x-2"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                  </svg>
                  <span>搜索</span>
                </button>
                
                <button 
                  type="button" 
                  id="clear-history"
                  class="text-gray-500 hover:text-gray-700 transition-colors text-sm"
                >
                  清除历史
                </button>
              </div>
            </form>
          </div>
          
          <!-- Search History -->
          <div class="p-6">
            <h3 class="text-sm font-medium text-gray-700 mb-3">搜索历史</h3>
            <div id="search-history" class="space-y-2 max-h-40 overflow-y-auto">
              <p class="text-sm text-gray-500">暂无搜索历史</p>
            </div>
          </div>
          
          <!-- Search Results -->
          <div id="search-results" class="hidden p-6 border-t border-gray-200 max-h-96 overflow-y-auto">
            <h3 class="text-sm font-medium text-gray-700 mb-3">搜索结果</h3>
            <div id="search-results-grid" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
              <!-- 搜索结果将在这里显示 -->
            </div>
          </div>
          
          <!-- Loading State -->
          <div id="search-loading" class="hidden p-6 text-center">
            <div class="inline-flex items-center space-x-2">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600"></div>
              <span class="text-sm text-gray-600">搜索中...</span>
            </div>
          </div>
        </div>
      </div>
    `;
    
    document.body.appendChild(this.panel);
  }

  bindEvents() {
    // 关闭按钮
    const closeBtn = this.panel.querySelector('#close-search');
    closeBtn.addEventListener('click', () => this.hide());
    
    // 点击背景关闭
    this.panel.addEventListener('click', (e) => {
      if (e.target === this.panel) this.hide();
    });
    
    // 搜索表单
    const searchForm = this.panel.querySelector('#search-form');
    searchForm.addEventListener('submit', (e) => this.handleSearch(e));
    
    // 清除历史
    const clearHistoryBtn = this.panel.querySelector('#clear-history');
    clearHistoryBtn.addEventListener('click', () => this.clearHistory());
    
    // 搜索输入框
    const searchInput = this.panel.querySelector('#search-input');
    searchInput.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') this.hide();
    });
    
    // ESC键关闭
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isVisible) {
        this.hide();
      }
    });
  }

  show() {
    if (this.isVisible) return;
    
    this.isVisible = true;
    this.panel.classList.remove('hidden');
    
    // 入场动画
    const content = this.panel.querySelector('.bg-white');
    content.style.transform = 'scale(0.9)';
    content.style.opacity = '0';
    
    animate([
      {
        targets: this.panel,
        opacity: [0, 1],
        duration: 200,
        ease: 'easeOutQuad'
      },
      {
        targets: content,
        scale: [0.9, 1],
        opacity: [0, 1],
        duration: 300,
        delay: 100,
        ease: 'easeOutBack'
      }
    ]);
    
    // 聚焦搜索框
    setTimeout(() => {
      this.panel.querySelector('#search-input').focus();
    }, 300);
  }

  hide() {
    if (!this.isVisible) return;
    
    const content = this.panel.querySelector('.bg-white');
    
    animate([
      {
        targets: content,
        scale: [1, 0.9],
        opacity: [1, 0],
        duration: 200,
        ease: 'easeInQuad'
      },
      {
        targets: this.panel,
        opacity: [1, 0],
        duration: 200,
        delay: 100,
        ease: 'easeInQuad',
        complete: () => {
          this.panel.classList.add('hidden');
          this.isVisible = false;
        }
      }
    ]);
  }

  async handleSearch(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const query = this.panel.querySelector('#search-input').value.trim();
    const source = this.panel.querySelector('#search-source').value;
    const count = parseInt(this.panel.querySelector('#search-count').value);
    
    if (!query) {
      this.showError('请输入搜索关键词');
      return;
    }
    
    this.currentQuery = query;
    this.showLoading(true);
    this.hideResults();
    
    try {
      const results = await this.dataCollector.searchImages(query, source, {
        per_page: count
      });
      
      this.addToHistory(query, source, results.length);
      this.showResults(results);
      
    } catch (error) {
      console.error('搜索失败:', error);
      this.showError('搜索失败，请重试');
    } finally {
      this.showLoading(false);
    }
  }

  showLoading(show) {
    const loading = this.panel.querySelector('#search-loading');
    const searchBtn = this.panel.querySelector('#search-btn');
    
    if (show) {
      loading.classList.remove('hidden');
      searchBtn.disabled = true;
      searchBtn.innerHTML = `
        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
        <span>搜索中...</span>
      `;
    } else {
      loading.classList.add('hidden');
      searchBtn.disabled = false;
      searchBtn.innerHTML = `
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
        <span>搜索</span>
      `;
    }
  }

  showResults(results) {
    const resultsContainer = this.panel.querySelector('#search-results');
    const resultsGrid = this.panel.querySelector('#search-results-grid');
    
    resultsGrid.innerHTML = '';
    
    if (results.length === 0) {
      resultsGrid.innerHTML = '<p class="col-span-full text-center text-gray-500">未找到相关图片</p>';
    } else {
      results.forEach((image, index) => {
        const imageElement = this.createResultImageElement(image, index);
        resultsGrid.appendChild(imageElement);
      });
    }
    
    resultsContainer.classList.remove('hidden');
    
    // 结果入场动画
    const images = resultsGrid.querySelectorAll('.result-image');
    animate(images, {
      scale: [0.8, 1],
      opacity: [0, 1],
      duration: 400,
      delay: (el, i) => i * 50,
      ease: 'easeOutQuart'
    });
  }

  createResultImageElement(image, index) {
    const element = document.createElement('div');
    element.className = 'result-image relative group cursor-pointer rounded-lg overflow-hidden bg-gray-100';
    element.style.opacity = '0';
    
    element.innerHTML = `
      <img 
        src="${image.thumbnail}" 
        alt="${image.title}"
        class="w-full h-24 object-cover transition-transform duration-300 group-hover:scale-110"
        loading="lazy"
      />
      <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center">
        <button class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-white text-gray-900 px-2 py-1 rounded text-xs font-medium">
          添加
        </button>
      </div>
    `;
    
    // 添加点击事件
    element.addEventListener('click', () => this.addImageToGallery(image));
    
    return element;
  }

  addImageToGallery(image) {
    // 添加图片到主图库
    if (window.app && window.app.imageGallery) {
      window.app.imageGallery.addImages([image]);
      
      // 显示成功提示
      this.showSuccess('图片已添加到图库');
    }
  }

  hideResults() {
    const resultsContainer = this.panel.querySelector('#search-results');
    resultsContainer.classList.add('hidden');
  }

  addToHistory(query, source, resultCount) {
    const historyItem = {
      query,
      source,
      resultCount,
      timestamp: new Date()
    };
    
    // 避免重复
    this.searchHistory = this.searchHistory.filter(item => 
      item.query !== query || item.source !== source
    );
    
    this.searchHistory.unshift(historyItem);
    
    // 限制历史记录数量
    if (this.searchHistory.length > 10) {
      this.searchHistory = this.searchHistory.slice(0, 10);
    }
    
    this.updateHistoryDisplay();
    this.saveHistory();
  }

  updateHistoryDisplay() {
    const historyContainer = this.panel.querySelector('#search-history');
    
    if (this.searchHistory.length === 0) {
      historyContainer.innerHTML = '<p class="text-sm text-gray-500">暂无搜索历史</p>';
      return;
    }
    
    historyContainer.innerHTML = this.searchHistory.map(item => `
      <div class="flex items-center justify-between p-2 bg-gray-50 rounded cursor-pointer hover:bg-gray-100 transition-colors history-item" 
           data-query="${item.query}" data-source="${item.source}">
        <div class="flex-1">
          <span class="text-sm font-medium">${item.query}</span>
          <span class="text-xs text-gray-500 ml-2">${item.source} • ${item.resultCount}张</span>
        </div>
        <span class="text-xs text-gray-400">${this.formatTime(item.timestamp)}</span>
      </div>
    `).join('');
    
    // 绑定历史记录点击事件
    historyContainer.querySelectorAll('.history-item').forEach(item => {
      item.addEventListener('click', () => {
        const query = item.dataset.query;
        const source = item.dataset.source;
        
        this.panel.querySelector('#search-input').value = query;
        this.panel.querySelector('#search-source').value = source;
      });
    });
  }

  clearHistory() {
    this.searchHistory = [];
    this.updateHistoryDisplay();
    this.saveHistory();
  }

  saveHistory() {
    try {
      localStorage.setItem('searchHistory', JSON.stringify(this.searchHistory));
    } catch (error) {
      console.warn('保存搜索历史失败:', error);
    }
  }

  loadHistory() {
    try {
      const saved = localStorage.getItem('searchHistory');
      if (saved) {
        this.searchHistory = JSON.parse(saved).map(item => ({
          ...item,
          timestamp: new Date(item.timestamp)
        }));
        this.updateHistoryDisplay();
      }
    } catch (error) {
      console.warn('加载搜索历史失败:', error);
    }
  }

  formatTime(date) {
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) return '刚刚';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
    return `${Math.floor(diff / 86400000)}天前`;
  }

  showError(message) {
    // 简单的错误提示
    console.error(message);
    alert(message);
  }

  showSuccess(message) {
    // 简单的成功提示
    console.log(message);
    // 可以扩展为更好的通知系统
  }
}
