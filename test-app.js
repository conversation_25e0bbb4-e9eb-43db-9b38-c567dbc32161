// 简单的应用测试脚本
const puppeteer = require('puppeteer');

async function testApp() {
  console.log('🧪 开始测试应用...');
  
  let browser;
  try {
    // 启动浏览器
    browser = await puppeteer.launch({
      headless: false, // 显示浏览器窗口
      devtools: true   // 打开开发者工具
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
      console.log(`浏览器控制台: ${msg.text()}`);
    });
    
    // 监听错误
    page.on('error', err => {
      console.error(`页面错误: ${err.message}`);
    });
    
    page.on('pageerror', err => {
      console.error(`页面脚本错误: ${err.message}`);
    });
    
    // 访问应用
    console.log('📱 访问应用页面...');
    await page.goto('http://localhost:3000?quick=true', {
      waitUntil: 'networkidle0',
      timeout: 30000
    });
    
    // 等待页面加载
    await page.waitForTimeout(3000);
    
    // 检查基本元素
    console.log('🔍 检查页面元素...');
    
    const title = await page.title();
    console.log(`页面标题: ${title}`);
    
    // 检查关键元素是否存在
    const elements = {
      'image-gallery': '图片库容器',
      'data-collection-btn': '数据采集按钮',
      'search-btn': '搜索按钮',
      'settings-btn': '设置按钮',
      'worker-monitor-btn': 'Worker监控按钮'
    };
    
    for (const [id, name] of Object.entries(elements)) {
      const element = await page.$(`#${id}`);
      if (element) {
        console.log(`✅ ${name} 存在`);
      } else {
        console.log(`❌ ${name} 不存在`);
      }
    }
    
    // 检查应用对象
    const appExists = await page.evaluate(() => {
      return typeof window.app !== 'undefined';
    });
    
    if (appExists) {
      console.log('✅ 应用对象存在');
      
      // 获取应用状态
      const appStatus = await page.evaluate(() => {
        const app = window.app;
        return {
          isInitialized: app.isInitialized,
          hasImageGallery: !!app.imageGallery,
          hasDataCollector: !!app.dataCollector,
          hasAnimationController: !!app.animationController,
          hasWorkerManager: !!app.workerManager
        };
      });
      
      console.log('应用状态:', appStatus);
    } else {
      console.log('❌ 应用对象不存在');
    }
    
    // 等待快速测试结果
    console.log('⏳ 等待快速测试完成...');
    await page.waitForTimeout(5000);
    
    // 检查测试结果
    const testResults = await page.$('#quick-test-results');
    if (testResults) {
      console.log('✅ 快速测试已运行');
      
      const testText = await page.evaluate(() => {
        const results = document.getElementById('quick-test-results');
        return results ? results.textContent : '无结果';
      });
      
      console.log('测试结果摘要:', testText.substring(0, 200) + '...');
    } else {
      console.log('❌ 快速测试未运行');
    }
    
    // 测试按钮点击
    console.log('🖱️ 测试按钮交互...');
    
    try {
      await page.click('#data-collection-btn');
      console.log('✅ 数据采集按钮可点击');
      await page.waitForTimeout(1000);
    } catch (error) {
      console.log('❌ 数据采集按钮点击失败:', error.message);
    }
    
    try {
      await page.click('#search-btn');
      console.log('✅ 搜索按钮可点击');
      await page.waitForTimeout(1000);
    } catch (error) {
      console.log('❌ 搜索按钮点击失败:', error.message);
    }
    
    // 截图
    await page.screenshot({ 
      path: 'test-screenshot.png',
      fullPage: true
    });
    console.log('📸 已保存截图: test-screenshot.png');
    
    console.log('✅ 应用测试完成');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    if (browser) {
      // 保持浏览器打开以便查看
      console.log('🌐 浏览器保持打开状态，请手动关闭');
      // await browser.close();
    }
  }
}

// 检查是否安装了puppeteer
try {
  require.resolve('puppeteer');
  testApp();
} catch (error) {
  console.log('❌ 未安装 puppeteer');
  console.log('请运行: npm install puppeteer');
  console.log('或者手动访问: http://localhost:3000?quick=true');
}
