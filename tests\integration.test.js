// 集成测试
class IntegrationTest {
  constructor() {
    this.testResults = [];
    this.app = null;
  }

  async runAllTests() {
    console.log('🧪 开始运行集成测试...');
    
    try {
      // 等待应用初始化
      await this.waitForApp();
      
      // 运行各项测试
      await this.testAppInitialization();
      await this.testDataCollection();
      await this.testImageGallery();
      await this.testSearchFunctionality();
      await this.testWorkerManager();
      await this.testAnimations();
      await this.testSettings();
      
      // 输出测试结果
      this.outputResults();
      
    } catch (error) {
      console.error('❌ 集成测试失败:', error);
      this.addResult('Integration Test', false, error.message);
    }
  }

  async waitForApp() {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      let attempts = 0;

      const checkApp = () => {
        attempts++;
        console.log(`等待应用初始化... 尝试 ${attempts}/100`);

        // 检查应用是否存在
        if (!window.app) {
          console.log('window.app 不存在');
        } else if (!window.app.isInitialized) {
          console.log('应用存在但未初始化完成');
        } else {
          console.log('✅ 应用初始化完成');
          this.app = window.app;
          resolve();
          return;
        }

        // 超时检查
        if (Date.now() - startTime > 15000) { // 增加到15秒
          const errorMsg = `应用初始化超时 (${attempts} 次尝试)`;
          console.error(errorMsg);
          reject(new Error(errorMsg));
          return;
        }

        setTimeout(checkApp, 150); // 增加检查间隔
      };

      checkApp();
    });
  }

  async testAppInitialization() {
    console.log('测试应用初始化...');
    
    try {
      // 检查主要组件是否存在
      this.assert(this.app.imageGallery, '图片库组件应该存在');
      this.assert(this.app.dataCollector, '数据采集器应该存在');
      this.assert(this.app.animationController, '动画控制器应该存在');
      this.assert(this.app.workerManager, 'Worker管理器应该存在');
      this.assert(this.app.searchPanel, '搜索面板应该存在');
      this.assert(this.app.settingsPanel, '设置面板应该存在');
      this.assert(this.app.workerMonitor, 'Worker监控器应该存在');
      
      // 检查DOM元素
      this.assert(document.getElementById('image-gallery'), '图片库容器应该存在');
      this.assert(document.getElementById('data-collection-btn'), '数据采集按钮应该存在');
      this.assert(document.getElementById('search-btn'), '搜索按钮应该存在');
      this.assert(document.getElementById('settings-btn'), '设置按钮应该存在');
      this.assert(document.getElementById('worker-monitor-btn'), 'Worker监控按钮应该存在');
      
      this.addResult('App Initialization', true, '应用初始化成功');
      
    } catch (error) {
      this.addResult('App Initialization', false, error.message);
    }
  }

  async testDataCollection() {
    console.log('测试数据采集功能...');
    
    try {
      const dataCollector = this.app.dataCollector;
      
      // 测试配置获取
      const config = dataCollector.getCollectionConfig();
      this.assert(config.batchSize > 0, '批次大小应该大于0');
      this.assert(config.maxImages > 0, '最大图片数应该大于0');
      
      // 测试API状态
      const apiStatus = dataCollector.getApiStatus();
      this.assert(typeof apiStatus === 'object', 'API状态应该是对象');
      
      // 测试模拟数据采集
      const mockImages = await dataCollector.collectMockData();
      this.assert(Array.isArray(mockImages), '模拟数据应该是数组');
      this.assert(mockImages.length > 0, '应该生成模拟图片数据');
      
      // 验证图片数据结构
      const firstImage = mockImages[0];
      this.assert(firstImage.id, '图片应该有ID');
      this.assert(firstImage.url, '图片应该有URL');
      this.assert(firstImage.title, '图片应该有标题');
      this.assert(Array.isArray(firstImage.tags), '图片应该有标签数组');
      
      this.addResult('Data Collection', true, `成功生成 ${mockImages.length} 张模拟图片`);
      
    } catch (error) {
      this.addResult('Data Collection', false, error.message);
    }
  }

  async testImageGallery() {
    console.log('测试图片库功能...');
    
    try {
      const imageGallery = this.app.imageGallery;
      
      // 测试视图模式切换
      const originalMode = imageGallery.viewMode;
      imageGallery.setViewMode('list');
      this.assert(imageGallery.viewMode === 'list', '应该能切换到列表视图');
      
      imageGallery.setViewMode('masonry');
      this.assert(imageGallery.viewMode === 'masonry', '应该能切换到瀑布流视图');
      
      imageGallery.setViewMode(originalMode);
      
      // 测试图片添加
      const testImages = [
        {
          id: 'test_1',
          url: 'https://picsum.photos/300/200?random=test1',
          title: '测试图片1',
          tags: ['test']
        }
      ];
      
      imageGallery.addImages(testImages);
      this.assert(imageGallery.images.length > 0, '应该能添加图片到图库');
      
      this.addResult('Image Gallery', true, '图片库功能正常');
      
    } catch (error) {
      this.addResult('Image Gallery', false, error.message);
    }
  }

  async testSearchFunctionality() {
    console.log('测试搜索功能...');
    
    try {
      const searchPanel = this.app.searchPanel;
      
      // 测试搜索面板显示/隐藏
      searchPanel.show();
      this.assert(searchPanel.isVisible, '搜索面板应该能显示');
      
      searchPanel.hide();
      this.assert(!searchPanel.isVisible, '搜索面板应该能隐藏');
      
      // 测试搜索历史
      searchPanel.addToHistory('test query', 'all', 10);
      this.assert(searchPanel.searchHistory.length > 0, '应该能添加搜索历史');
      
      searchPanel.clearHistory();
      this.assert(searchPanel.searchHistory.length === 0, '应该能清除搜索历史');
      
      this.addResult('Search Functionality', true, '搜索功能正常');
      
    } catch (error) {
      this.addResult('Search Functionality', false, error.message);
    }
  }

  async testWorkerManager() {
    console.log('测试Worker管理器...');

    try {
      const workerManager = this.app.workerManager;

      // 测试Worker状态
      const stats = workerManager.getWorkerStats();
      this.assert(typeof stats === 'object', 'Worker统计应该是对象');
      this.assert(typeof stats.workers === 'object', 'Worker统计应该包含workers对象');

      // 如果有Worker，测试任务提交
      if (stats.totalWorkers > 0) {
        try {
          const result = await Promise.race([
            workerManager.submitTask('imageProcessor', 'extractMetadata', {
              imageUrl: 'https://picsum.photos/300/200?random=test'
            }),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Worker任务超时')), 5000))
          ]);

          this.assert(result, '应该能提交并完成Worker任务');
          this.addResult('Worker Manager', true, `Worker管理器功能正常 (${stats.totalWorkers} 个Worker)`);
        } catch (workerError) {
          console.warn('Worker任务测试失败:', workerError.message);
          this.addResult('Worker Manager', true, `Worker管理器基础功能正常，但任务执行失败: ${workerError.message}`);
        }
      } else {
        console.warn('没有可用的Worker，跳过任务测试');
        this.addResult('Worker Manager', true, 'Worker管理器基础功能正常 (无可用Worker)');
      }

    } catch (error) {
      this.addResult('Worker Manager', false, error.message);
    }
  }

  async testAnimations() {
    console.log('测试动画功能...');
    
    try {
      const animationController = this.app.animationController;
      
      // 测试基本动画
      const testElement = document.createElement('div');
      testElement.style.opacity = '0';
      document.body.appendChild(testElement);
      
      const animation = animationController.animate(testElement, 'fadeIn');
      this.assert(animation, '应该能创建动画');
      
      // 测试粒子系统
      const particleSystem = animationController.createParticleSystem(document.body);
      this.assert(particleSystem, '应该能创建粒子系统');
      
      // 清理
      testElement.remove();
      
      this.addResult('Animations', true, '动画功能正常');
      
    } catch (error) {
      this.addResult('Animations', false, error.message);
    }
  }

  async testSettings() {
    console.log('测试设置功能...');
    
    try {
      const settingsPanel = this.app.settingsPanel;
      
      // 测试设置获取
      const settings = settingsPanel.getSettings();
      this.assert(typeof settings === 'object', '设置应该是对象');
      this.assert(settings.apis, '设置应该包含API配置');
      this.assert(settings.collection, '设置应该包含采集配置');
      this.assert(settings.display, '设置应该包含显示配置');
      this.assert(settings.performance, '设置应该包含性能配置');
      
      // 测试设置更新
      const originalBatchSize = settings.collection.batchSize;
      settingsPanel.updateSetting('collection.batchSize', 25);
      
      const updatedSettings = settingsPanel.getSettings();
      this.assert(updatedSettings.collection.batchSize === 25, '应该能更新设置');
      
      // 恢复原设置
      settingsPanel.updateSetting('collection.batchSize', originalBatchSize);
      
      this.addResult('Settings', true, '设置功能正常');
      
    } catch (error) {
      this.addResult('Settings', false, error.message);
    }
  }

  assert(condition, message) {
    if (!condition) {
      throw new Error(message);
    }
  }

  addResult(testName, passed, message) {
    this.testResults.push({
      name: testName,
      passed,
      message,
      timestamp: new Date().toISOString()
    });
  }

  outputResults() {
    console.log('\n📊 测试结果汇总:');
    console.log('='.repeat(50));
    
    let passedCount = 0;
    let failedCount = 0;
    
    this.testResults.forEach(result => {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`${status} ${result.name}: ${result.message}`);
      
      if (result.passed) {
        passedCount++;
      } else {
        failedCount++;
      }
    });
    
    console.log('='.repeat(50));
    console.log(`总计: ${this.testResults.length} 个测试`);
    console.log(`通过: ${passedCount} 个`);
    console.log(`失败: ${failedCount} 个`);
    console.log(`成功率: ${((passedCount / this.testResults.length) * 100).toFixed(1)}%`);
    
    // 在页面上显示结果
    this.displayResultsOnPage();
  }

  displayResultsOnPage() {
    const resultContainer = document.createElement('div');
    resultContainer.id = 'test-results';
    resultContainer.className = 'fixed top-4 left-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 z-50 max-w-md max-h-96 overflow-y-auto';
    
    const passedCount = this.testResults.filter(r => r.passed).length;
    const totalCount = this.testResults.length;
    
    resultContainer.innerHTML = `
      <div class="flex justify-between items-center mb-3">
        <h3 class="text-lg font-semibold">测试结果</h3>
        <button onclick="this.parentElement.parentElement.remove()" class="text-gray-500 hover:text-gray-700">×</button>
      </div>
      <div class="mb-3">
        <div class="text-sm text-gray-600">
          通过: ${passedCount}/${totalCount} (${((passedCount / totalCount) * 100).toFixed(1)}%)
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
          <div class="bg-green-500 h-2 rounded-full" style="width: ${(passedCount / totalCount) * 100}%"></div>
        </div>
      </div>
      <div class="space-y-2">
        ${this.testResults.map(result => `
          <div class="flex items-center space-x-2 text-sm">
            <span class="${result.passed ? 'text-green-500' : 'text-red-500'}">${result.passed ? '✓' : '✗'}</span>
            <span class="font-medium">${result.name}</span>
          </div>
        `).join('')}
      </div>
    `;
    
    document.body.appendChild(resultContainer);
  }
}

// 自动运行测试（如果在测试环境中）
if (typeof window !== 'undefined' && window.location.search.includes('test=true')) {
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
      const test = new IntegrationTest();
      test.runAllTests();
    }, 2000); // 等待应用完全加载
  });
}

// 导出测试类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = IntegrationTest;
}
