import { animate, utils } from 'animejs';

export class ParticleSystem {
  constructor(container, options = {}) {
    this.container = typeof container === 'string' ? 
      document.querySelector(container) : container;
    
    this.options = {
      particleCount: 50,
      particleSize: 2,
      particleColor: '#3b82f6',
      speed: 1,
      direction: 'random', // 'up', 'down', 'left', 'right', 'random'
      lifetime: 3000,
      fadeOut: true,
      interactive: false,
      ...options
    };
    
    this.particles = [];
    this.isRunning = false;
    this.animationId = null;
    this.mouseX = 0;
    this.mouseY = 0;
    
    this.init();
  }

  init() {
    if (!this.container) {
      console.error('Particle system container not found');
      return;
    }
    
    // 确保容器有相对定位
    if (getComputedStyle(this.container).position === 'static') {
      this.container.style.position = 'relative';
    }
    
    // 绑定鼠标事件（如果启用交互）
    if (this.options.interactive) {
      this.bindMouseEvents();
    }
  }

  bindMouseEvents() {
    this.container.addEventListener('mousemove', (e) => {
      const rect = this.container.getBoundingClientRect();
      this.mouseX = e.clientX - rect.left;
      this.mouseY = e.clientY - rect.top;
    });
    
    this.container.addEventListener('click', (e) => {
      const rect = this.container.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      this.burst(x, y, 10);
    });
  }

  createParticle(x, y, customOptions = {}) {
    const particle = document.createElement('div');
    particle.className = 'particle';
    
    const options = { ...this.options, ...customOptions };
    
    // 设置粒子样式
    particle.style.cssText = `
      position: absolute;
      width: ${options.particleSize}px;
      height: ${options.particleSize}px;
      background-color: ${options.particleColor};
      border-radius: 50%;
      pointer-events: none;
      z-index: 1000;
      left: ${x}px;
      top: ${y}px;
    `;
    
    // 计算运动方向
    const velocity = this.calculateVelocity(options.direction, options.speed);
    
    // 粒子数据
    const particleData = {
      element: particle,
      x: x,
      y: y,
      vx: velocity.x,
      vy: velocity.y,
      life: options.lifetime,
      maxLife: options.lifetime,
      size: options.particleSize,
      color: options.particleColor,
      fadeOut: options.fadeOut
    };
    
    this.container.appendChild(particle);
    this.particles.push(particleData);
    
    return particleData;
  }

  calculateVelocity(direction, speed) {
    switch (direction) {
      case 'up':
        return { x: 0, y: -speed };
      case 'down':
        return { x: 0, y: speed };
      case 'left':
        return { x: -speed, y: 0 };
      case 'right':
        return { x: speed, y: 0 };
      case 'random':
      default:
        const angle = Math.random() * Math.PI * 2;
        return {
          x: Math.cos(angle) * speed,
          y: Math.sin(angle) * speed
        };
    }
  }

  updateParticles() {
    const containerRect = this.container.getBoundingClientRect();
    
    for (let i = this.particles.length - 1; i >= 0; i--) {
      const particle = this.particles[i];
      
      // 更新位置
      particle.x += particle.vx;
      particle.y += particle.vy;
      
      // 应用重力（可选）
      if (this.options.gravity) {
        particle.vy += this.options.gravity;
      }
      
      // 交互效果
      if (this.options.interactive) {
        const dx = this.mouseX - particle.x;
        const dy = this.mouseY - particle.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance < 50) {
          const force = (50 - distance) / 50;
          particle.vx -= (dx / distance) * force * 0.1;
          particle.vy -= (dy / distance) * force * 0.1;
        }
      }
      
      // 更新生命周期
      particle.life -= 16; // 假设60fps
      
      // 更新DOM元素
      particle.element.style.left = particle.x + 'px';
      particle.element.style.top = particle.y + 'px';
      
      // 淡出效果
      if (particle.fadeOut) {
        const opacity = particle.life / particle.maxLife;
        particle.element.style.opacity = opacity;
      }
      
      // 移除死亡的粒子
      if (particle.life <= 0 || 
          particle.x < -10 || particle.x > containerRect.width + 10 ||
          particle.y < -10 || particle.y > containerRect.height + 10) {
        particle.element.remove();
        this.particles.splice(i, 1);
      }
    }
  }

  start() {
    if (this.isRunning) return;
    
    this.isRunning = true;
    this.animate();
  }

  stop() {
    this.isRunning = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }
  }

  animate() {
    if (!this.isRunning) return;
    
    this.updateParticles();
    
    // 自动生成新粒子
    if (this.options.autoGenerate && this.particles.length < this.options.particleCount) {
      const x = Math.random() * this.container.offsetWidth;
      const y = Math.random() * this.container.offsetHeight;
      this.createParticle(x, y);
    }
    
    this.animationId = requestAnimationFrame(() => this.animate());
  }

  // 在指定位置爆发粒子
  burst(x, y, count = 10) {
    for (let i = 0; i < count; i++) {
      const angle = (Math.PI * 2 * i) / count;
      const speed = 2 + Math.random() * 3;
      
      this.createParticle(x, y, {
        direction: 'custom',
        speed: speed,
        particleColor: this.getRandomColor(),
        lifetime: 1000 + Math.random() * 2000
      });
      
      // 为自定义方向设置速度
      const lastParticle = this.particles[this.particles.length - 1];
      lastParticle.vx = Math.cos(angle) * speed;
      lastParticle.vy = Math.sin(angle) * speed;
    }
  }

  // 创建文字粒子效果
  textEffect(text, x, y) {
    const textElement = document.createElement('div');
    textElement.textContent = text;
    textElement.style.cssText = `
      position: absolute;
      left: ${x}px;
      top: ${y}px;
      font-size: 14px;
      font-weight: bold;
      color: ${this.options.particleColor};
      pointer-events: none;
      z-index: 1001;
    `;
    
    this.container.appendChild(textElement);
    
    // 文字动画
    animate(textElement, {
      translateY: -50,
      opacity: [1, 0],
      scale: [1, 1.2],
      duration: 2000,
      ease: 'easeOutQuart',
      complete: () => {
        textElement.remove();
      }
    });
  }

  // 创建轨迹效果
  trail(startX, startY, endX, endY, particleCount = 20) {
    for (let i = 0; i < particleCount; i++) {
      const progress = i / particleCount;
      const x = startX + (endX - startX) * progress;
      const y = startY + (endY - startY) * progress;
      
      setTimeout(() => {
        this.createParticle(x, y, {
          lifetime: 500,
          particleSize: 1 + Math.random() * 2,
          particleColor: this.getRandomColor()
        });
      }, i * 50);
    }
  }

  getRandomColor() {
    const colors = [
      '#3b82f6', '#ef4444', '#10b981', '#f59e0b', 
      '#8b5cf6', '#ec4899', '#06b6d4', '#84cc16'
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  }

  // 清除所有粒子
  clear() {
    this.particles.forEach(particle => {
      particle.element.remove();
    });
    this.particles = [];
  }

  // 更新配置
  updateOptions(newOptions) {
    this.options = { ...this.options, ...newOptions };
  }

  // 销毁粒子系统
  destroy() {
    this.stop();
    this.clear();
    
    if (this.options.interactive) {
      this.container.removeEventListener('mousemove', this.bindMouseEvents);
      this.container.removeEventListener('click', this.bindMouseEvents);
    }
  }

  // 预设效果
  presets = {
    snow: {
      particleCount: 100,
      particleSize: 3,
      particleColor: '#ffffff',
      direction: 'down',
      speed: 0.5,
      gravity: 0.01,
      autoGenerate: true
    },
    
    confetti: {
      particleCount: 50,
      particleSize: 4,
      direction: 'random',
      speed: 3,
      lifetime: 3000,
      interactive: true
    },
    
    magic: {
      particleCount: 30,
      particleSize: 2,
      particleColor: '#ffd700',
      direction: 'up',
      speed: 1,
      fadeOut: true,
      autoGenerate: true
    },
    
    fireflies: {
      particleCount: 20,
      particleSize: 3,
      particleColor: '#ffff00',
      direction: 'random',
      speed: 0.3,
      lifetime: 5000,
      interactive: true
    }
  };

  // 应用预设
  applyPreset(presetName) {
    if (this.presets[presetName]) {
      this.updateOptions(this.presets[presetName]);
    }
  }
}
