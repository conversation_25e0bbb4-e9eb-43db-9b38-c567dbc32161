import { animate, utils } from 'animejs';
import { ImageGallery } from './components/ImageGallery.js';
import { DataCollector } from './services/DataCollector.js';
import { AnimationController } from './utils/AnimationController.js';
import { WorkerManager } from './workers/WorkerManager.js';
import { SearchPanel } from './components/SearchPanel.js';
import { SettingsPanel } from './components/SettingsPanel.js';
import { WorkerMonitor } from './components/WorkerMonitor.js';

class App {
  constructor() {
    this.imageGallery = null;
    this.dataCollector = null;
    this.animationController = null;
    this.workerManager = null;
    this.searchPanel = null;
    this.settingsPanel = null;
    this.workerMonitor = null;
    this.isInitialized = false;
  }

  async init() {
    try {
      console.log('🚀 初始化应用...');
      
      // 初始化组件
      this.animationController = new AnimationController();
      this.workerManager = new WorkerManager();
      this.dataCollector = new DataCollector(this.workerManager);
      this.imageGallery = new ImageGallery();
      this.searchPanel = new SearchPanel(this.dataCollector);
      this.settingsPanel = new SettingsPanel(this.dataCollector, this.animationController);
      this.workerMonitor = new WorkerMonitor(this.workerManager);

      // 绑定事件
      this.bindEvents();
      
      // 启动动画
      this.startInitialAnimations();

      // 初始化高级动画功能
      this.initAdvancedAnimations();

      // 隐藏加载屏幕
      await this.hideLoadingScreen();
      
      this.isInitialized = true;
      console.log('✅ 应用初始化完成');
      
    } catch (error) {
      console.error('❌ 应用初始化失败:', error);
      this.showError('应用初始化失败，请刷新页面重试');
    }
  }

  bindEvents() {
    // 搜索按钮
    const searchBtn = document.getElementById('search-btn');
    if (searchBtn) {
      searchBtn.addEventListener('click', () => this.handleSearch());
    }

    // 数据采集按钮
    const dataCollectionBtn = document.getElementById('data-collection-btn');
    if (dataCollectionBtn) {
      dataCollectionBtn.addEventListener('click', () => this.handleDataCollection());
    }

    // Worker监控按钮
    const workerMonitorBtn = document.getElementById('worker-monitor-btn');
    if (workerMonitorBtn) {
      workerMonitorBtn.addEventListener('click', () => this.handleWorkerMonitor());
    }

    // 设置按钮
    const settingsBtn = document.getElementById('settings-btn');
    if (settingsBtn) {
      settingsBtn.addEventListener('click', () => this.handleSettings());
    }

    // 刷新图库按钮
    const refreshBtn = document.getElementById('refresh-gallery');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => this.handleRefreshGallery());
    }

    // 视图模式切换
    const viewModeSelect = document.getElementById('view-mode');
    if (viewModeSelect) {
      viewModeSelect.addEventListener('change', (e) => this.handleViewModeChange(e.target.value));
    }

    // 窗口大小变化
    window.addEventListener('resize', utils.debounce(() => {
      this.imageGallery?.handleResize();
    }, 250));

    // 添加搜索快捷键
    document.addEventListener('keydown', (e) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        this.searchPanel.show();
      }
    });
  }

  initAdvancedAnimations() {
    // 创建粒子系统
    this.animationController.createParticleSystem(document.body, {
      particleCount: 30,
      particleSize: 2,
      particleColor: '#3b82f6',
      direction: 'random',
      speed: 0.5,
      autoGenerate: false,
      interactive: true
    });

    // 初始化图片库动画
    this.animationController.createImageGalleryAnimations();

    // 创建滚动进度条
    const progressBar = document.createElement('div');
    progressBar.className = 'fixed top-0 left-0 h-1 bg-primary-600 z-50 transition-all duration-300';
    progressBar.style.width = '0%';
    document.body.appendChild(progressBar);

    this.animationController.createScrollProgress(progressBar);

    // 为统计卡片添加计数器动画
    this.animationController.createCounterAnimation('#total-images');

    // 为页面元素添加滚动动画
    this.animationController.createScrollAnimation('main > section', {
      animation: 'fadeInUp',
      stagger: 200,
      threshold: 0.2
    });
  }

  handleSearch() {
    // 显示搜索面板
    this.searchPanel.show();
  }

  async handleDataCollection() {
    try {
      const btn = document.getElementById('data-collection-btn');
      const originalText = btn.textContent;
      
      // 更新按钮状态
      btn.textContent = '采集中...';
      btn.disabled = true;
      
      // 更新状态显示
      this.updateCollectionStatus('采集中');
      
      // 开始数据采集
      await this.dataCollector.startCollection();
      
      // 恢复按钮状态
      btn.textContent = originalText;
      btn.disabled = false;
      
      // 更新状态
      this.updateCollectionStatus('完成');

      // 显示成功动画
      const btnRect = btn.getBoundingClientRect();
      this.animationController.showSuccessAnimation('数据采集完成！', {
        x: btnRect.left + btnRect.width / 2,
        y: btnRect.top + btnRect.height / 2
      });

    } catch (error) {
      console.error('数据采集失败:', error);
      this.showError('数据采集失败，请重试');
      
      // 恢复按钮状态
      const btn = document.getElementById('data-collection-btn');
      btn.textContent = '数据采集';
      btn.disabled = false;
      this.updateCollectionStatus('错误');
    }
  }

  handleWorkerMonitor() {
    // 显示Worker监控面板
    this.workerMonitor.show();
  }

  handleSettings() {
    // 显示设置面板
    this.settingsPanel.show();
  }

  async handleRefreshGallery() {
    const btn = document.getElementById('refresh-gallery');
    
    // 旋转动画
    animate(btn, {
      rotate: '360deg',
      duration: 500,
      ease: 'easeOutQuad'
    });

    // 刷新图库
    await this.imageGallery.refresh();
  }

  handleViewModeChange(mode) {
    this.imageGallery.setViewMode(mode);
  }

  startInitialAnimations() {
    // 页面元素入场动画
    const elements = document.querySelectorAll('header, main > section');
    
    animate(elements, {
      translateY: [30, 0],
      opacity: [0, 1],
      duration: 800,
      delay: (el, i) => i * 100,
      ease: 'easeOutQuart'
    });

    // 统计卡片动画
    const statCards = document.querySelectorAll('main > section:first-child > div');
    
    animate(statCards, {
      scale: [0.9, 1],
      opacity: [0, 1],
      duration: 600,
      delay: (el, i) => 200 + i * 150,
      ease: 'easeOutBack'
    });
  }

  async hideLoadingScreen() {
    const loadingScreen = document.getElementById('loading-screen');
    
    if (loadingScreen) {
      await new Promise(resolve => {
        animate(loadingScreen, {
          opacity: [1, 0],
          duration: 500,
          ease: 'easeOutQuad',
          complete: () => {
            loadingScreen.style.display = 'none';
            resolve();
          }
        });
      });
    }
  }

  updateCollectionStatus(status) {
    const statusElement = document.getElementById('collection-status');
    if (statusElement) {
      statusElement.textContent = status;
      
      // 状态变化动画
      animate(statusElement, {
        scale: [1, 1.1, 1],
        duration: 300,
        ease: 'easeOutQuad'
      });
    }
  }

  updateStats(stats) {
    // 更新总图片数
    const totalImages = document.getElementById('total-images');
    if (totalImages && stats.totalImages !== undefined) {
      this.animateNumber(totalImages, parseInt(totalImages.textContent) || 0, stats.totalImages);
    }

    // 更新处理速度
    const processingSpeed = document.getElementById('processing-speed');
    if (processingSpeed && stats.processingSpeed !== undefined) {
      processingSpeed.textContent = `${stats.processingSpeed}/s`;
    }
  }

  animateNumber(element, from, to) {
    const obj = { value: from };
    
    animate(obj, {
      value: to,
      duration: 1000,
      ease: 'easeOutQuad',
      update: () => {
        element.textContent = Math.round(obj.value);
      }
    });
  }

  showError(message) {
    // 简单的错误提示，后续可以扩展为更复杂的通知系统
    console.error(message);
    alert(message);
  }
}

// 应用启动
document.addEventListener('DOMContentLoaded', async () => {
  const app = new App();

  // 立即将app实例挂载到全局，便于测试
  window.app = app;

  try {
    await app.init();
    console.log('🎉 应用启动成功');
  } catch (error) {
    console.error('❌ 应用启动失败:', error);
    app.showError('应用启动失败: ' + error.message);
  }
});

// 导出App类供其他模块使用
export { App };
