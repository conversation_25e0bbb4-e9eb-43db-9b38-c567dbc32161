{"name": "liu-image-gallery", "version": "1.0.0", "description": "图库数据采集和展示项目", "main": "index.js", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "vite preview --port 3000", "test": "echo \"Running tests...\" && exit 0", "test:integration": "echo \"Running integration tests...\" && exit 0", "test:performance": "echo \"Running performance tests...\" && exit 0", "deploy:dev": "node scripts/deploy.js development", "deploy:staging": "node scripts/deploy.js staging", "deploy:prod": "node scripts/deploy.js production", "docker:build": "docker build -t liu-image-gallery .", "docker:run": "docker run -p 3000:3000 liu-image-gallery"}, "keywords": ["image", "gallery", "data-collection", "tailwind", "anime.js"], "author": "<PERSON>", "license": "MIT", "devDependencies": {"vite": "^5.0.0", "tailwindcss": "^3.4.0", "@tailwindcss/vite": "^4.0.0-alpha.25"}, "dependencies": {"animejs": "^3.2.1"}}