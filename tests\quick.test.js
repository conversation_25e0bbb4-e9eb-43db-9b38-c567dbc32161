// 快速测试 - 基本功能验证
class QuickTest {
  constructor() {
    this.testResults = [];
  }

  async runQuickTests() {
    console.log('🚀 开始快速测试...');
    
    try {
      // 基础DOM测试
      this.testDOMElements();
      
      // 等待应用加载
      await this.waitForBasicApp();
      
      // 基础功能测试
      this.testBasicFunctionality();
      
      // 输出结果
      this.outputResults();
      
    } catch (error) {
      console.error('❌ 快速测试失败:', error);
      this.addResult('Quick Test', false, error.message);
      this.outputResults();
    }
  }

  testDOMElements() {
    console.log('测试DOM元素...');
    
    try {
      // 检查基本DOM元素
      this.assert(document.getElementById('image-gallery'), '图片库容器应该存在');
      this.assert(document.getElementById('data-collection-btn'), '数据采集按钮应该存在');
      this.assert(document.getElementById('search-btn'), '搜索按钮应该存在');
      this.assert(document.getElementById('settings-btn'), '设置按钮应该存在');
      this.assert(document.getElementById('worker-monitor-btn'), 'Worker监控按钮应该存在');
      
      // 检查样式
      const gallery = document.getElementById('image-gallery');
      const styles = window.getComputedStyle(gallery);
      this.assert(styles.display !== 'none', '图片库应该是可见的');
      
      this.addResult('DOM Elements', true, '所有必需的DOM元素都存在');
      
    } catch (error) {
      this.addResult('DOM Elements', false, error.message);
    }
  }

  async waitForBasicApp() {
    console.log('等待应用基础加载...');
    
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      let attempts = 0;
      
      const checkApp = () => {
        attempts++;
        
        // 检查window.app是否存在
        if (window.app) {
          console.log('✅ 应用对象已加载');
          resolve();
          return;
        }
        
        // 超时检查 (减少到5秒)
        if (Date.now() - startTime > 5000) {
          console.warn('⚠️ 应用加载超时，但继续测试基础功能');
          resolve(); // 不拒绝，继续测试
          return;
        }
        
        setTimeout(checkApp, 100);
      };
      
      checkApp();
    });
  }

  testBasicFunctionality() {
    console.log('测试基础功能...');
    
    try {
      // 测试按钮点击
      const dataCollectionBtn = document.getElementById('data-collection-btn');
      if (dataCollectionBtn) {
        // 模拟点击事件
        const clickEvent = new Event('click');
        dataCollectionBtn.dispatchEvent(clickEvent);
        this.addResult('Button Click', true, '按钮点击事件正常');
      }
      
      // 测试应用对象（如果存在）
      if (window.app) {
        this.testAppObject();
      } else {
        this.addResult('App Object', false, '应用对象未加载');
      }
      
      // 测试CSS类
      this.testCSSClasses();
      
      // 测试JavaScript基础功能
      this.testJavaScriptFeatures();
      
    } catch (error) {
      this.addResult('Basic Functionality', false, error.message);
    }
  }

  testAppObject() {
    try {
      const app = window.app;
      
      // 检查基本属性
      this.assert(typeof app === 'object', '应用应该是对象');
      
      // 检查组件是否存在（不要求已初始化）
      const components = ['imageGallery', 'dataCollector', 'animationController', 'workerManager'];
      let loadedComponents = 0;
      
      components.forEach(component => {
        if (app[component]) {
          loadedComponents++;
        }
      });
      
      this.addResult('App Components', true, `${loadedComponents}/${components.length} 个组件已加载`);
      
      // 测试初始化状态
      if (app.isInitialized) {
        this.addResult('App Initialization', true, '应用已完全初始化');
      } else {
        this.addResult('App Initialization', false, '应用未完全初始化');
      }
      
    } catch (error) {
      this.addResult('App Object', false, error.message);
    }
  }

  testCSSClasses() {
    try {
      // 检查Tailwind CSS是否加载
      const testElement = document.createElement('div');
      testElement.className = 'bg-blue-500 text-white p-4';
      document.body.appendChild(testElement);
      
      const styles = window.getComputedStyle(testElement);
      const hasBackground = styles.backgroundColor !== 'rgba(0, 0, 0, 0)' && styles.backgroundColor !== 'transparent';
      
      testElement.remove();
      
      if (hasBackground) {
        this.addResult('CSS Framework', true, 'Tailwind CSS 正常加载');
      } else {
        this.addResult('CSS Framework', false, 'Tailwind CSS 可能未正确加载');
      }
      
    } catch (error) {
      this.addResult('CSS Framework', false, error.message);
    }
  }

  testJavaScriptFeatures() {
    try {
      // 测试ES6+特性
      const testArrowFunction = () => 'arrow function works';
      const testDestructuring = { a: 1, b: 2 };
      const { a, b } = testDestructuring;
      const testTemplateString = `Template string: ${a + b}`;
      
      this.assert(testArrowFunction() === 'arrow function works', '箭头函数应该工作');
      this.assert(a === 1 && b === 2, '解构赋值应该工作');
      this.assert(testTemplateString === 'Template string: 3', '模板字符串应该工作');
      
      // 测试Promise
      const testPromise = new Promise(resolve => resolve('promise works'));
      this.assert(testPromise instanceof Promise, 'Promise应该可用');
      
      // 测试fetch API
      this.assert(typeof fetch === 'function', 'Fetch API应该可用');
      
      // 测试Web Workers支持
      this.assert(typeof Worker === 'function', 'Web Workers应该被支持');
      
      this.addResult('JavaScript Features', true, '现代JavaScript特性支持正常');
      
    } catch (error) {
      this.addResult('JavaScript Features', false, error.message);
    }
  }

  assert(condition, message) {
    if (!condition) {
      throw new Error(message);
    }
  }

  addResult(testName, passed, message) {
    this.testResults.push({
      name: testName,
      passed,
      message,
      timestamp: new Date().toISOString()
    });
  }

  outputResults() {
    console.log('\n📊 快速测试结果:');
    console.log('='.repeat(40));
    
    let passedCount = 0;
    let failedCount = 0;
    
    this.testResults.forEach(result => {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`${status} ${result.name}: ${result.message}`);
      
      if (result.passed) {
        passedCount++;
      } else {
        failedCount++;
      }
    });
    
    console.log('='.repeat(40));
    console.log(`总计: ${this.testResults.length} 个测试`);
    console.log(`通过: ${passedCount} 个`);
    console.log(`失败: ${failedCount} 个`);
    console.log(`成功率: ${((passedCount / this.testResults.length) * 100).toFixed(1)}%`);
    
    // 在页面上显示结果
    this.displayResultsOnPage();
  }

  displayResultsOnPage() {
    // 移除之前的结果
    const existingResults = document.getElementById('quick-test-results');
    if (existingResults) {
      existingResults.remove();
    }
    
    const resultContainer = document.createElement('div');
    resultContainer.id = 'quick-test-results';
    resultContainer.className = 'fixed top-4 left-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 z-50 max-w-sm';
    
    const passedCount = this.testResults.filter(r => r.passed).length;
    const totalCount = this.testResults.length;
    const successRate = ((passedCount / totalCount) * 100).toFixed(1);
    
    resultContainer.innerHTML = `
      <div class="flex justify-between items-center mb-3">
        <h3 class="text-lg font-semibold">快速测试</h3>
        <button onclick="this.parentElement.parentElement.remove()" class="text-gray-500 hover:text-gray-700">×</button>
      </div>
      <div class="mb-3">
        <div class="text-2xl font-bold ${successRate >= 80 ? 'text-green-600' : successRate >= 60 ? 'text-yellow-600' : 'text-red-600'}">${successRate}%</div>
        <div class="text-sm text-gray-600">${passedCount}/${totalCount} 项通过</div>
        <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
          <div class="bg-green-500 h-2 rounded-full" style="width: ${successRate}%"></div>
        </div>
      </div>
      <div class="space-y-1 max-h-40 overflow-y-auto">
        ${this.testResults.map(result => `
          <div class="flex items-center space-x-2 text-sm">
            <span class="${result.passed ? 'text-green-500' : 'text-red-500'}">${result.passed ? '✓' : '✗'}</span>
            <span class="font-medium">${result.name}</span>
          </div>
        `).join('')}
      </div>
      <div class="mt-3 text-xs text-gray-500 text-center">
        快速测试完成于 ${new Date().toLocaleTimeString()}
      </div>
    `;
    
    document.body.appendChild(resultContainer);
  }
}

// 自动运行快速测试
if (typeof window !== 'undefined' && window.location.search.includes('quick=true')) {
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
      const test = new QuickTest();
      test.runQuickTests();
    }, 1000); // 等待1秒让页面基本加载完成
  });
}

// 导出测试类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = QuickTest;
}
